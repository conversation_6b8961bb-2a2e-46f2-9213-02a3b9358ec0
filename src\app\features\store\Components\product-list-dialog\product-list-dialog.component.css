.dialog-header h2 {
  font-family: 'cairo';
}

.form-row.two-fields {
  display: flex;
  gap: 16px;
}

.form-row.two-fields .form-field {
  flex: 1;
}
.unitOf{
  margin-top: 16px;
}
/* .form-field {
  width: 100%;
} */

.image-card {
  max-width: 400px;
  margin: 16px auto;
  direction: rtl;
  font-family: 'Cairo', sans-serif;
  border-radius: 12px;
  overflow: hidden;
}

.file-input {
  display: block;
  margin: 8px 0;
  padding: 8px;
  border: 1px dashed #ccc;
  border-radius: 8px;
  background-color: #f8f9fa;
  width: 100%;
  font-size: 14px;
}

.file-input:focus {
  outline: none;
  border-color: #2196f3;
}


.image-preview-inline {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
}

.image-preview-inline .preview-thumb {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 8px;
  border: 2px solid #ddd;
}


.image-display {
  text-align: center;
  margin: 16px 0;
}

.image-wrapper {
  position: relative;
  display: inline-block;
}

.display-image {
  width: 200px;
  height: 200px;
  object-fit: cover;
  border-radius: 12px;
  border: 3px solid #eee;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}


.remove-overlay-btn {
  position: absolute;
  top: -12px;
  left: -12px;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  transform: scale(0.9);
  transition: transform 0.2s;


}

.remove-overlay-btn {
  position: absolute;
  top: -12px;
  left: -12px;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  transform: scale(0.9);
  transition: transform 0.2s;


}

.remove-overlay-btn:hover {
  transform: scale(1.1);
}

.image-display:empty::before {
  content: "لم تُرفَع صورة بعد";
  color: #999;
  font-style: italic;
}