<div class="dialog-container">
  <div mat-dialog-title class="dialog-header">
    <mat-icon>{{ isEditMode ? 'edit' : 'person_add' }}</mat-icon>
    <h2>{{ isEditMode ? 'تعديل المعاملة ' : 'إضافة معاملة جديدة' }}</h2>
  </div>

  <div mat-dialog-content class="dialog-content">
    <form [formGroup]="form" (ngSubmit)="onSubmit()">

     
            <div class="form-row">
                <mat-form-field class="form-field">
                    <mat-label>تاريخ المعاملة</mat-label>
                    <input matInput [matDatepicker]="picker" formControlName="transfersDate" required>
                    <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                    <mat-datepicker #picker></mat-datepicker>
                        <mat-error *ngIf="form.get('transfersDate')?.hasError('required')">
                        تاريخ المعاملة مطلوب
                        </mat-error>
                </mat-form-field>
            </div>

           <div class="form-row seller-buyer">
             <div class="field-container">
              <mat-form-field class="form-field">
                <mat-label>البائع</mat-label>
                <mat-select formControlName="sellerId" required>
                <mat-option *ngFor="let rep of partners" [value]="rep.id">
                    {{ rep.name }}
                </mat-option>
                </mat-select>
              </mat-form-field>
              <mat-error *ngIf="form.get('sellerId')?.hasError('required')">
              البائع مطلوب
            </mat-error>
             </div>

            <div class="field-container">
              <mat-form-field class="form-field">
                <mat-label>المشترى</mat-label>
                <mat-select formControlName="buyerId" required>
                <mat-option *ngFor="let rep of partners" [value]="rep.id">
                    {{ rep.name }}
                </mat-option>
                </mat-select>
              </mat-form-field>
              <mat-error *ngIf="form.get('buyerId')?.hasError('required')">
              المشترى مطلوب
             </mat-error> 
              <mat-error *ngIf="form.get('buyerId')?.hasError('sameAsSeller')">
               لا يمكن أن يكون البائع هو نفس المشترى
            </mat-error>    
            </div>
           </div>

           <div class="form-row">
           <mat-form-field class="form-field adjust-width">
                <mat-label>عدد الاسهم</mat-label>
                <input matInput type="number"
                    step="1"
                    min="1"  formControlName="sharesCount" type="number" placeholder="أدخل عدد الاسهم">
                <mat-icon matSuffix>compare_arrows</mat-icon>
                <mat-error *ngIf="form.get('sharesCount')?.invalid && form.get('sharesCount')?.touched">
                    {{ getErrorMessage('sharesCount') }}
                </mat-error>
           </mat-form-field>
          </div>
           <div class="form-row">
            <mat-form-field class="form-field">
                <mat-label>المبلغ</mat-label>
                <input matInput type="number"
                    step="1.00"
                    min="1"  formControlName="transferAmount" type="number" placeholder="أدخل المبلغ">
                <mat-icon matSuffix>ج.م</mat-icon>
                <mat-error *ngIf="form.get('transferAmount')?.invalid && form.get('transferAmount')?.touched">
                    {{ getErrorMessage('transferAmount') }}
                </mat-error>
            </mat-form-field>
          </div>
    </form>
  </div>

  <div mat-dialog-actions class="dialog-actions">
    <button mat-button (click)="onCancel()" [disabled]="loading">
      إلغاء
    </button>
    <button mat-raised-button 
            color="primary" 
            (click)="onSubmit()" 
            [disabled]="form.invalid || loading">
      <mat-spinner *ngIf="loading" diameter="20"></mat-spinner>
      <mat-icon *ngIf="!loading">{{ isEditMode ? 'save' : 'add' }}</mat-icon>
      {{ isEditMode ? 'حفظ التغييرات' : 'إضافة معاملة' }}
    </button>
  </div>
</div>


