
.categories-section {
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  background-color: #fff;
  font-family: 'Cairo', sans-serif;
}

.tree-header {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  border-bottom: 1px solid #eee;
  background-color: #f9f9f9;
}

.tree-header mat-label {
  font-weight: 600;
  color: #333;
}

.tree-search-field {
  width: 100%;
}

.category-tree {
  padding: 8px 0;
  max-height: 400px;
  overflow-y: auto;
}

.tree-node {
  padding: 6px 16px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f0f0f0;
  }

  &.expandable {
    padding: 4px 0;
  }
}

.node-content {
  display: flex;
  align-items: center;
  flex: 1;
  padding: 8px;
  border-radius: 6px;

  &.selected {
    background-color: #e3f2fd;
    color: #1976d2;
  }
}

.icon,
.folder-icon {
  margin-right: 8px;
  font-size: 18px !important;
}

.folder-icon {
  color: #ff9800;
}

.toggle-btn {
  width: 32px;
  height: 32px;
  margin-right: 8px;
  transform: rotate(0deg);
  transition: transform 0.2s;
}

[aria-expanded='true'] .toggle-btn mat-icon,
.tree-node.expandable[matTreeNodeToggle] mat-icon {
  transform: rotate(0deg);
}