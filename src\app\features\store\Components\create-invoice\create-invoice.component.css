.full-width {
  width: 100%;
}

.row {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 12px;
}

.col {
  flex: 1;
  min-width: 150px;
}

.actions {
  margin-top: 12px;
  display: flex;
  justify-content: flex-start;
  gap: 8px;
}

.mt-8 {
  margin-top: 8px;
}

.mt-12 {
  margin-top: 12px;
}


table {
  
  overflow-y: scroll;
 
}

/* جعل جدول Material يلائم */
table.full-width {
  width: 100%;
}

table .mat-mdc-cell{
  padding: 8px;
  border-bottom: 1px solid #eee;
  
}
table .mat-mdc-cell input{
  max-width: 80px;
  padding: 4px;
  font-size: 1rem;
  border-radius: 8px;
  border: 1px solid #ccc;


}
.product-option{
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    border-bottom: 2px solid rgb(3, 170, 248);
}

.balance{
  color: red;
}
.unit{
  color: blue;
}
