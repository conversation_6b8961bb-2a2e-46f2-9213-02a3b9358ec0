<div class="products-container">
  <!-- Header -->
  <div class="page-header">
    <h1 class="page-title">
      <mat-icon>receipt_long</mat-icon>
      إدارة الاصناف و المنتجات
    </h1>

    <button mat-raised-button color="success" class="create-button" (click)="exportToViews()">
      <mat-icon>picture_as_pdf</mat-icon>
      عرض PDF
    </button>
    <div *appHasPermission="['Permissions.Stores.Add']">
      <button mat-raised-button color="primary" class="create-button" (click)="createProduct()">
        <mat-icon>add</mat-icon>
        إنشاء صنف جديدة
      </button>
    </div>
  </div>

  <!-- Filters -->
  <mat-card class="filters-card">
    <mat-card-content>
      <div class="filters-row">
        <mat-form-field class="search-field">
          <mat-label>البحث</mat-label>
          <input matInput [(ngModel)]="searchTerm" (keyup.enter)="onSearch()"
            placeholder="البحث بالصنف او الكود او الباركود">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>

        <!-- <mat-form-field class="filter-field" >
          <mat-label>الصتنيفات</mat-label>P
          <mat-select [(ngModel)]="selectedCategoryId" (selectionChange)="onFilterChange()">
            <mat-option [value]="undefined">الكل</mat-option>
            <mat-option *ngFor="let rep of categories" [value]="rep.id">
              {{ rep.name }}
            </mat-option>
          </mat-select>
        </mat-form-field> -->
        <div class="stock-field">
          <!-- Stock Status -->
          <mat-form-field class="filter-field" appearance="fill">
            <mat-label>حالة المخزون</mat-label>
            <mat-select [(ngModel)]="selectedStockStatus" (selectionChange)="onFilterChange()">
              <mat-option value="">جميع الحالات</mat-option>
              <mat-option [value]="'instock'">متوفر</mat-option>
              <mat-option [value]="'lowstock'">كمية قليلة</mat-option>
              <mat-option [value]="'outofstock'">غير متوفر</mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <!-- Categories Tree -->
        <div class="categories-section">
          <div class="tree-header">
            <mat-label>التصنيفات</mat-label>
            <mat-form-field appearance="outline" class="tree-search-field">
              <input matInput placeholder="ابحث عن تصنيف..." #searchInput (input)="filterCategories(searchInput.value)">
              <mat-icon matSuffix>search</mat-icon>
            </mat-form-field>
          </div>

          <mat-tree #tree [dataSource]="dataSourceCategories" [childrenAccessor]="childrenAccessor"
            class="category-tree">

            <!-- Leaf Node -->
            <mat-tree-node *matTreeNodeDef="let node" class="tree-node" [class.has-match]="node.hasMatch">
              <mat-checkbox [checked]="isChecked(node)" (change)="toggleSelection(node)">
                <li class="mat-tree-node">
                  <span [innerHTML]="highlight(node.name, searchTerm)"></span>
                </li>
              </mat-checkbox>
            </mat-tree-node>

            <!-- Expandable Node -->
            <mat-tree-node *matTreeNodeDef="let node; when: hasChild" matTreeNodePadding isExpandable
              [isExpanded]="expandedNodes" class="tree-node expandable"   [class.has-match]="node.hasMatch"
                [class.has-child-match]="hasChildMatch(node)" >
              <button mat-icon-button matTreeNodeToggle class="toggle-btn">
                <mat-icon>
                  {{ tree.isExpanded(node) ? 'expand_more' : 'chevron_right' }}
                </mat-icon>
              </button>

              <mat-checkbox [checked]="isChecked(node)" (change)="toggleSelection(node)">
                <li class="mat-tree-node">
                  <span [innerHTML]="highlight(node.name, searchTerm)"></span>
                </li>
              </mat-checkbox>
            </mat-tree-node>
          </mat-tree>
        </div>







        <!-- <mat-tree #tree [dataSource]="dataSourceCategories" [childrenAccessor]="childrenAccessor" class="category-tree">
 <mat-tree-node *matTreeNodeDef="let node" matTreeNodePadding >
    
    <button matIconButton disabled ></button>
    <span (click)="selectCategory(node.id)" 
          [class.selected]="selectedCategoryId === node.id">
      {{node.name}}
    </span>
  </mat-tree-node>

 <mat-tree-node *matTreeNodeDef="let node;when: hasChild" matTreeNodePadding matTreeNodeToggle
                 [cdkTreeNodeTypeaheadLabel]="node.name">
    <button matIconButton matTreeNodeToggle 
            [attr.aria-label]="'Toggle ' + node.name">
      <mat-icon class="mat-icon-ltr-mirror">
        {{tree.isExpanded(node) ? 'expand_more' : 'chevron_left'}}
      </mat-icon>
     
    </button>
      <span (click)="selectCategory(node.id)" 
          [class.selected]="selectedCategoryId === node.id">
      {{node.name}}
    </span>
  </mat-tree-node>


</mat-tree> -->
        <div class="filter-actions">
          <button mat-raised-button color="primary" (click)="onSearch()">
            <mat-icon>search</mat-icon>
            بحث
          </button>
          <button mat-button (click)="clearFilters()">
            <mat-icon>clear</mat-icon>
            مسح الفلاتر
          </button>
        </div>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Loading Spinner -->
  <div *ngIf="loading" class="loading-container">
    <mat-spinner></mat-spinner>
  </div>

  <!-- Product Table -->
  <mat-card *ngIf="!loading" class="table-card">
    <mat-card-content>
      <div class="table-container">
        <table mat-table [dataSource]="Products" class="products-table">

          <!-- Product Code Column -->
          <ng-container matColumnDef="code">
            <th mat-header-cell *matHeaderCellDef>كود الصنف</th>
            <td mat-cell *matCellDef="let prod">
              {{ prod.code}}
            </td>
          </ng-container>

          <!-- Product Barcode Column -->
          <ng-container matColumnDef="barcode">
            <th mat-header-cell *matHeaderCellDef>الباركود</th>
            <td mat-cell *matCellDef="let prod">
              {{ prod.barcode}}
            </td>
          </ng-container>

          <!-- Product Name Column -->
          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef>اسم الصنف</th>
            <td mat-cell *matCellDef="let prod">
              <div class="name-cell">
                <strong> {{ prod.name}} </strong>
                <div class="description" *ngIf="prod.description">
                  {{ prod.description }}
                </div>
              </div>
            </td>
          </ng-container>

          <!-- Product Category Column -->
          <ng-container matColumnDef="categoryName">
            <th mat-header-cell *matHeaderCellDef>التصنيف</th>
            <td mat-cell *matCellDef="let prod">
              {{ prod.categoryName || '-' }}
            </td>
          </ng-container>

          <!-- Product Balance Column -->
          <ng-container matColumnDef="balance">
            <th mat-header-cell *matHeaderCellDef>الرصيد</th>
            <td mat-cell *matCellDef="let prod">
              {{ prod.balance || '-' }}
              <br>
              <span *ngIf="prod.balance <= prod.minimumStock" class="low-stock">الكمية قليلة</span>
              <span *ngIf="prod.balance === 0" class="out-of-stock">غير متوفر</span>
              <span *ngIf="prod.balance > prod.minimumStock" class="in-stock">متوفر</span>
            </td>
          </ng-container>



          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>الإجراءات</th>
            <td mat-cell *matCellDef="let prod">
              <div class="actions-cell">
                <div *appHasPermission="['Permissions.Stores.Edit']">
                  <button mat-icon-button color="primary" matTooltip="تعديل" (click)="editProduct(prod)">
                    <mat-icon>edit</mat-icon>
                  </button>
                </div>
                <div *appHasPermission="['Permissions.Stores.Delete']">
                  <button mat-icon-button color="warn" matTooltip="حذف" (click)="deleteProduct(prod)">
                    <mat-icon>delete</mat-icon>
                  </button>
                </div>
              </div>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>

        <!-- No Data Message -->
        <div *ngIf="Products.length === 0" class="no-data">
          <mat-icon>receipt_long</mat-icon>
          <p>لا توجد حركات للشركاء</p>
        </div>
      </div>

      <!-- Pagination -->
      <mat-paginator *ngIf="totalCount > 0" [length]="totalCount" [pageSize]="pageSize"
        [pageSizeOptions]="[5, 10, 25, 50]" [pageIndex]="pageNumber - 1" (page)="onPageChange($event)"
        showFirstLastButtons>
      </mat-paginator>
    </mat-card-content>
  </mat-card>
</div>