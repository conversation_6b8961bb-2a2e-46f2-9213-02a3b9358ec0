.drawer-container {
  height: calc(100vh - 64px);
  background: #f5f5f5;

}

.filters-drawer {
  width: 320px;
  padding: 16px;
  background: #fff;
  box-shadow: 2px 0 8px rgba(0,0,0,0.1);

}



.drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  font-weight: 600;
  color: #333;
}

.full-width {
  width: 100%;
  margin-bottom: 16px;
}

.categories-section {
  margin-top: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.tree-header {
  padding: 8px 12px;
  background: #f9f9f9;
  border-bottom: 1px solid #e0e0e0;
}

.tree-search-field {
  width: 100%;
  margin-top: 8px;
}

.category-tree {
  padding: 8px 0;
  max-height: 400px;
  overflow-y: auto;
}

.tree-node {
  padding: 6px 12px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.node-content {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-family: 'Cairo', '<PERSON>o', sans-serif !important;
}

.node-name {
  font-size: 14px;
}

.selected {
  font-weight: bold;
  color: #1976d2;
}

.toggle-btn {
  width: 32px;
  height: 32px;
}

.drawer-actions {
  padding: 16px 0;
  margin-top: auto;
}

/* Main Content */
.content {
  padding: 16px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 12px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.5rem;
  color: #333;
}

.header-export{
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.type-filters { 
  display: flex;
  align-items: space-between;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 16px;
  width: 50%;
  margin: auto;
}

.mat-button-toggle-checked { /* الزرّ المختار */
  font-weight: 600;
}

.action-btn {
  margin-left: 8px;
}

.products-table {
  width: 100%;
  border-collapse: collapse;
}

.mat-column-code, .mat-column-barcode {
  max-width: 100px;
}

.desc {
  font-size: 0.85rem;
  color: #666;
  margin-top: 4px;
}

.status {
  font-size: 0.8rem;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.in-stock {
  background: #e8f5e9;
  color: #2e7d32;
}

.low-stock {
  background: #fff8e1;
  color: #c25a00;
}

.out-of-stock {
  background: #ffebee;
  color: #c62828;
}

.image-product{
  object-fit: contain;
  max-width: 100px;
  max-height: 100px;
}



.actions-cell {
  flex-direction: row;
  gap: 4px;
  justify-content: flex-end;
  flex-wrap: wrap;
}
.actions-cell button {
  min-width: 36px;
  min-height: 36px;
}

.actions-cell button:hover {
  transform: scale(1.1);
  transition: transform 0.2s ease;
}
.no-data {
  text-align: center;
  padding: 40px 0;
  color: #999;
}

.no-data mat-icon {
  font-size: 48px;
}

.loading-container {
  display: flex;
  justify-content: center;
  margin: 40px 0;
}

::ng-deep .folder-icon {
  color: rgb(179, 179, 2) !important; /* Material Primary Blue */
  border: #333;
}

[dir="rtl"] .actions-cell {
  flex-direction: row-reverse;
}


.view-toggle {
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}

.view-toggle mat-button-toggle-group {
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

.view-toggle mat-icon {
  font-size: 18px;
}

/* Cards Layout */
.cards-container {
  margin-top: 16px;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  width: 100%;
}

.product-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: transform 0.2s, box-shadow 0.2s;
}

.product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}


.card-image {
  height: 160px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f9f9f9;
}

.product-image {
  max-width: 90%;
  max-height: 140px;
  object-fit: contain;
}


.card-image img {
  max-width: 100%;

  max-height: 120px;
  object-fit: contain;
}


.card-content {
  padding: 16px;
  flex: 1;
}

.card-content h3 {
  margin: 0 0 8px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.card-content p {
  margin: 4px 0;
  font-size: 0.9rem;
  color: #555;
}

.stock-status {
  margin-top: 8px;
}

.status {
  display: inline-block;
  font-size: 0.8rem;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.in-stock {
  background: #e8f5e9;
  color: #2e7d32;
}

.low-stock {
  background: #fff8e1;
  color: #c25a00;
}

.out-of-stock {
  background: #ffebee;
  color: #c62828;
}

.card-actions {
  padding: 8px 8px 8px 16px;
  margin-top: auto;
}

/* Responsive */
@media (max-width: 599px) {
  .cards-grid {
    grid-template-columns: 1fr; /* عمود واحد على الموبايل */
    gap: 12px;
  }

  .product-card {
    min-width: 100%;
  }
}









@media screen and (max-width: 768px) {
  .header-export {
    flex-direction: column;
  }

 .page-header {
    flex-direction: column;
    margin-right: 10px;
  }
   
  
  .action-btn {
    width: 100%;
    justify-content: center;
    margin-bottom: 8px;
  }


  .actions-cell {
    flex-direction: column;
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .type-filters { 
    flex-direction: column;
    flex-wrap: wrap;
    gap: 8px;
  
  }


}




.virtual-scroll-viewport {
  height: calc(100vh - 200px); 
  overflow-y: auto;
  margin: 16px 0;
}
.virtual-scroll-viewport  .cdk-virtual-scroll-content-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
  }


.cards-container {
  padding: 0 16px;
   margin-bottom: 16px;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  justify-items: center;
  width: 100%;
}

.product-card {
  width: 100%;
  max-width: 280px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;

  
}
.product-card :hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  }


.product-image {
  width: 100%;
  height: 150px;
  object-fit: cover;
}

.card-content {
  padding: 12px;
}
.card-content  h3 {
    margin: 0 0 8px 0;
    font-size: 1.1rem;
    font-weight: 600;
  }

.card-content  .desc {
    color: #666;
    font-size: 0.9rem;
    margin: 4px 0;
  }

.card-content  .code, .barcode, .category {
    font-size: 0.85rem;
    color: #555;
    margin: 4px 0;
  }


.card-actions {
  padding: 8px;
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.status {
  font-size: 0.8rem;
  padding: 2px 6px;
  border-radius: 4px;
  margin-right: 4px;
}
.status .in-stock {
    background-color: #e8f5e9;
    color: #2e7d32;
  }

.status  .low-stock {
    background-color: #fff8e1;
    color: #f57f17;
  }

 .status .out-of-stock {
    background-color: #ffebee;
    color: #c62828;
 }

 .loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  padding: 16px;
  color: #555;
  font-size: 0.9rem;
}