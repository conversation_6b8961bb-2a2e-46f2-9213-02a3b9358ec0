import { NgModule } from '@angular/core';
import { BrowserModule, provideClientHydration, withEventReplay } from '@angular/platform-browser';
import { authInterceptor } from './interceptors/auth.interceptor';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { LayoutComponent } from './layout/layout/layout.component';
import { MatIconModule } from '@angular/material/icon';
import { HttpClientModule, provideHttpClient, withFetch ,withInterceptors} from '@angular/common/http';
import { ImagePreviewDialogComponent } from './features/shared/image-preview-dialog/image-preview-dialog.component';
import { UnauthorizedComponent } from './features/shared/components/unauthorized/unauthorized.component';
import { SharedModule } from './shared/shared.module';
import { NgChartsModule } from 'ng2-charts';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

@NgModule({
  declarations: [
    AppComponent,
    LayoutComponent,
    ImagePreviewDialogComponent,
    UnauthorizedComponent
    
  ],
  imports: [
    BrowserModule,
    AppRoutingModule,
    MatSlideToggleModule,
    HttpClientModule,
     MatIconModule,
    SharedModule,
    BrowserModule,
    NgChartsModule,
    BrowserAnimationsModule


  ],
  providers: [
    provideClientHydration(withEventReplay()),
    provideHttpClient(withFetch(),withInterceptors([authInterceptor]))
    
    
  ],
  
  bootstrap: [AppComponent]
})
export class AppModule { }
