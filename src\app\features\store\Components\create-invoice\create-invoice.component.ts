import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { map, Observable, of, startWith } from 'rxjs';
import { CreateInvoice, Product } from '../../../../models/product.model';
import { ProductService } from '../../../../services/product.service';

@Component({
  selector: 'app-create-invoice',
  standalone: false,
  templateUrl: './create-invoice.component.html',
  styleUrl: './create-invoice.component.css'
})
export class CreateInvoiceComponent  implements OnInit {
  invoiceForm!: FormGroup;

  
  displayedColumns = ['barcode','product', 'qty', 'unitPrice', 'discount', 'total', 'actions'];

  constructor(private fb: FormBuilder, private cd: ChangeDetectorRef ,private productService: ProductService) {}

  ngOnInit(): void {
    // تهيئة النموذج (مطابق لهيكل الـ JSON المرسل)
    this.invoiceForm = this.fb.group({
    

      invoiceNumber: ['INV-001'],
      invoiceType: [1],
      invoiceDate: [new Date().toISOString()],
      partyId: [0, Validators.required], // العميل (party)
      
      subTotal: [0],
      itemDiscountAmount: [0],
      invoiceDiscountAmount: [0],
      taxAmount: [0],
      totalAmount: [0],
      paidAmount: [0],
      remainingAmount: [0],
      paymentType: [1],
      isPaid: [false],
      notes: [''],

      items: this.fb.array([])
    });

    // نضيف صف واحد افتراضي بعد إنشاء النموذج
    this.addItem();

    // نراقب التغيرات لحساب الإجماليات تلقائيًا
    this.invoiceForm.valueChanges.subscribe(() => this.calculateTotals());

        this.loadProducts();
     this.filteredProducts = this.productCtrl.valueChanges.pipe(
      startWith(''),
      map(value => this._filter(value || ''))
    );
  }

  /** getter للـ items */
  get items(): FormArray {
    return this.invoiceForm.get('items') as FormArray;
  }

  /** إضافة صف صنف جديد */
  addItem() {
    const group = this.fb.group({
      id: [0],
      name: [''],
      invoiceMasterId: [0],
      productId: [0, Validators.required],
      quantity: [1, [Validators.required, Validators.min(0.000001)]],
      unitPrice: [0, [Validators.required, Validators.min(0)]],
      discountPercentage: [0],
      discountAmount: [0],
      totalPrice: [0],
      notes: ['']
    });

    this.items.push(group);

    // نضمن أن Material Table يحدث نفسه
    this.cd.detectChanges();
  }

  /** حذف صف */
  removeItem(index: number) {
    this.items.removeAt(index);
   
    this.calculateTotals();
    this.invoiceForm.setControl('items', this.fb.array(this.items.controls));
     this.cd.detectChanges();
  }
 
  /** حساب إجمالي سطر محدد */
  calculateRowTotal(index: number) {
    const row = this.items.at(index);
    const qty = Number(row.get('quantity')?.value || 0);
    const price = Number(row.get('unitPrice')?.value || 0);
    const discountPerc = Number(row.get('discountPercentage')?.value || 0);

    const gross = qty * price;
    const discountAmount = (gross * discountPerc) / 100;

    const totalPrice = gross - discountAmount;

    row.patchValue({
      discountAmount: Number(discountAmount.toFixed(2)),
      totalPrice: Number(totalPrice.toFixed(2))
    }, { emitEvent: false }); // لا نُطلق valueChanges مرتين

    this.calculateTotals();
  }

  /** حساب الإجماليات (subTotal, discounts, tax, total, remaining) */
  calculateTotals() {
    const itemsVal = this.items.controls.map(c => c.value);

    const subTotal = itemsVal.reduce((s, it) => s + (Number(it.totalPrice || 0)), 0);
    const itemDiscountAmount = itemsVal.reduce((s, it) => s + (Number(it.discountAmount || 0)), 0);

    const invoiceDiscountAmount = Number(this.invoiceForm.get('invoiceDiscountAmount')?.value || 0);
    const taxAmount = Number(this.invoiceForm.get('taxAmount')?.value || 0); // يمكن أن يكون نسبة أو مبلغ حسب تصميمك

    // في النموذج الذي أرسلته taxAmount هو مبلغ، لكن إذا أردت نسبة عدّل الصيغة:
    // افترضنا هنا taxAmount هو مبلغ ثابت
    const totalAfterDiscounts = subTotal - itemDiscountAmount - invoiceDiscountAmount;
    const totalAmount = Number((totalAfterDiscounts + taxAmount).toFixed(2));

    const paid = Number(this.invoiceForm.get('paidAmount')?.value || 0);
    const remaining = Number((totalAmount - paid).toFixed(2));
    const isPaid = remaining <= 0;

    // patch نتائج الحسابات إلى النموذج
    this.invoiceForm.patchValue({
      subTotal: Number(subTotal.toFixed(2)),
      itemDiscountAmount: Number(itemDiscountAmount.toFixed(2)),
      totalAmount,
      remainingAmount: remaining,
      isPaid: isPaid
    }, { emitEvent: false });
  }

  /** تجهيز Payload مطابق للـ JSON المطلوب */
  preparePayload() {
    // نأخذ النموذج وننظف القيم غير الضرورية أو نضبط التواريخ
    const raw = this.invoiceForm.getRawValue();

    // نتأكد أن كل صنف يحتوي على الحقول المطلوبة وبصيغة صحيحة
    const items = (raw.items || []).map((it: any) => ({
      id: it.id || 0,
      name: it.name || '',
      invoiceMasterId: it.invoiceMasterId || 0,
      productId: Number(it.productId || 0),
      quantity: Number(it.quantity || 0),
      unitPrice: Number(it.unitPrice || 0),
      discountPercentage: Number(it.discountPercentage || 0),
      discountAmount: Number(it.discountAmount || 0),
      totalPrice: Number(it.totalPrice || 0),
      notes: it.notes || ''
    }));

    const payload = {
      id: raw.id || 0,
    
      invoiceNumber: raw.invoiceNumber || '',
      invoiceType: Number(raw.invoiceType || 1),
      invoiceDate: raw.invoiceDate || new Date().toISOString(),
      partyId: Number(raw.partyId || 0),
      subTotal: Number(raw.subTotal || 0),
      itemDiscountAmount: Number(raw.itemDiscountAmount || 0),
      invoiceDiscountAmount: Number(raw.invoiceDiscountAmount || 0),
      taxAmount: Number(raw.taxAmount || 0),
      totalAmount: Number(raw.totalAmount || 0),
      paidAmount: Number(raw.paidAmount || 0),
      remainingAmount: Number(raw.remainingAmount || 0),
      paymentType: Number(raw.paymentType || 1),
      isPaid: !!raw.isPaid,
      notes: raw.notes || '',
      items
    };

    return payload;
  }

  /** حفظ / إرسال الفاتورة (مثال - حالياً Console) */
  saveInvoice() {
    if (this.invoiceForm.invalid) {
      this.invoiceForm.markAllAsTouched();
      return;
    }

    // تحضير الأحمال
    const payload = this.preparePayload();

    // هنا تنادي الـ API الفعلي (مثال):
    // this.http.post('/api/invoices', payload).subscribe(...)

    console.log('Invoice Payload ->', payload);
    alert('تم تجهيز JSON في الكونسول (انظر Console).');
  }

  /** لتحسين الأداء مع *ngFor في الجدول */
  trackByIndex(index: number): number {
    return index;
  }


  productCtrl = new FormControl('');

  filteredProducts!: Observable<Product[]>;

  products: Product[] = [];
  invoiceItems: any[] = [];

  loadProducts() : void {
        this.productService
      .getProducts({
        pageNumber: 1,
        pageSize: 1000,
        searchTerm: null,
        isActive: true,
        categoryId: null,
        itemType: null,
        stockStatus: null,
      })
      .subscribe({
        next: (response) => {
          if (response.succeeded && response.data) {
            this.products = response.data.items;
           
          }
          
        },
        error: (error) => {
        
          this.products = [];
        },
      });
  }

 private _filter(value: string): Product[] {
    const filterValue = value;
    return this.products.filter(option =>
      option.name.includes(filterValue)
    );
  }

 addProduct(product: Product) {
  //   this.invoiceItems.push({ ...product, quantity: 1 });
     
  //   this.productCtrl.setValue(''); // مسح البحث بعد الاختيار
  //  //  this.items.push(product);




  
 const group = this.fb.group({
    id: [0],
    invoiceMasterId: [0],
    productId: [product.id, Validators.required],
    quantity: [1, [Validators.required, Validators.min(0.000001)]],
    unitPrice: [0, [Validators.required, Validators.min(0)]],
    discountPercentage: [0],
    discountAmount: [0],
    totalPrice: [0], // الكمية 1 × السعر
    notes: [''],
    name: [product.name] // عشان نعرض الاسم في الجدول
  });

  this.items.push(group);

  this.productCtrl.setValue(''); // يمسح البحث

    this.invoiceForm.setControl('items', this.fb.array(this.items.controls));
    this.cd.detectChanges();
    this.calculateTotals();

  }

}