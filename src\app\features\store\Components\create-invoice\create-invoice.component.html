<div class="container">
  <h2>إنشاء فاتورة جديدة</h2>

<form [formGroup]="invoiceForm" (ngSubmit)="saveInvoice()">

  <mat-card>
    <mat-card-title>إنشاء / تعديل فاتورة</mat-card-title>
    <mat-card-content>
      <div class="row">
        <mat-form-field appearance="outline" class="col">
          <mat-label>رقم الفاتورة</mat-label>
          <input matInput formControlName="invoiceNumber">
        </mat-form-field>

        <mat-form-field appearance="outline" class="col">
          <mat-label>تاريخ الفاتورة</mat-label>
          <input matInput type="datetime-local"
                 [value]="invoiceForm.get('invoiceDate')?.value | date:'yyyy-MM-ddTHH:mm'"
                 (change)="invoiceForm.patchValue({ invoiceDate: $event.target })">
        </mat-form-field>

        <mat-form-field appearance="outline" class="col">
          <mat-label>العميل (partyId)</mat-label>
          <input matInput formControlName="partyId" type="number">
        </mat-form-field>
      </div>
    </mat-card-content>
  </mat-card>

  <mat-card class="mt-12">
    <mat-card-title>تفاصيل الأصناف</mat-card-title>
    <mat-card-content>

    <mat-form-field style="width: 100%;">
    <input
        type="text"
        placeholder="ابحث عن صنف..."
        matInput
         [formControl]="productCtrl"
        [matAutocomplete]="auto">

    <mat-autocomplete #auto="matAutocomplete" (optionSelected)="addProduct($event.option.value)">
        <mat-option class="product-option" *ngFor="let product of filteredProducts | async" [value]="product">
        {{product.name}} - {{product.barcode}}
       <br/>
        الرصيد: <span class="balance"> {{product.balance || 0 | number:'1.2-2'}} </span>
        <span class="unit">{{product.unitName}} </span>
        </mat-option>
        <mat-option *ngIf="(filteredProducts | async)?.length === 0">
          لا توجد نتائج
        </mat-option>
    </mat-autocomplete>
    </mat-form-field>



      <!-- يجب أن يكون داخل formGroup لتعمل formArrayName -->
      <div formArrayName="items">
        <table mat-table [dataSource]="items.controls" class="mat-elevation-z1 full-width">

          <!-- Barcode / ProductId Column -->
          <ng-container matColumnDef="barcode">
            <th mat-header-cell *matHeaderCellDef>كود / باركود</th>
            <td mat-cell *matCellDef="let row; let i = index " [formGroupName]="i">
              <input matInput type="number" formControlName="productId" placeholder="productId">
            </td>
          </ng-container>

          <!-- Product name (readonly here, you can extend with autocomplete) -->
          <ng-container matColumnDef="product">
            <th mat-header-cell *matHeaderCellDef>الصنف</th>
            <td mat-cell *matCellDef="let row; let i = index " [formGroupName]="i">
              <!-- لو عندك إسم المنتج من API عرض هنا -->
              <span>{{ row.get('name')?.value || '-' }}</span> 
            </td>
          </ng-container>

          <!-- Qty -->
          <ng-container matColumnDef="qty">
            <th mat-header-cell *matHeaderCellDef>الكمية</th>
            <td mat-cell *matCellDef="let row; let i = index " [formGroupName]="i">
              <input matInput type="number" formControlName="quantity" (change)="calculateRowTotal(i)">
            </td>
          </ng-container>

          <!-- Unit Price -->
          <ng-container matColumnDef="unitPrice">
            <th mat-header-cell *matHeaderCellDef>سعر الوحدة</th>
            <td mat-cell *matCellDef="let row; let i = index" [formGroupName]="i">
              <input matInput type="number" formControlName="unitPrice" (change)="calculateRowTotal(i)">
            </td>
          </ng-container>

          <!-- Discount % -->
          <ng-container matColumnDef="discount">
            <th mat-header-cell *matHeaderCellDef>خصم %</th>
            <td mat-cell *matCellDef="let row; let i = index" [formGroupName]="i">
              <input matInput type="number" formControlName="discountPercentage" (change)="calculateRowTotal(i)">
            </td>
          </ng-container>

          <!-- Total -->
          <ng-container matColumnDef="total">
            <th mat-header-cell *matHeaderCellDef>الإجمالي</th>
            <td mat-cell *matCellDef="let row; let i = index" [formGroupName]="i">
              {{  row.get('totalPrice')?.value || 0 | number:'1.2-2' }}
            </td>
          </ng-container>

          <!-- Actions -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef></th>
            <td mat-cell *matCellDef="let row; let i =  index">
              <button mat-icon-button color="warn" type="button" (click)="removeItem(i)">
                <mat-icon>delete</mat-icon>
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns;"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>

        <div class="actions">
          <button mat-stroked-button color="primary" type="button" (click)="addItem()">+ إضافة صنف</button>
        </div>
      </div>

    </mat-card-content>
  </mat-card>

  <mat-card class="mt-12">
    <mat-card-title>الملخص والحسابات</mat-card-title>
    <mat-card-content>
      <div class="row">
        <mat-form-field appearance="outline" class="col">
          <mat-label>إجمالي الأصناف (subTotal)</mat-label>
          <input matInput [value]="invoiceForm.get('subTotal')?.value | number:'1.2-2'" readonly>
        </mat-form-field>

        <mat-form-field appearance="outline" class="col">
          <mat-label>خصم الفاتورة (invoiceDiscountAmount)</mat-label>
          <input matInput formControlName="invoiceDiscountAmount" type="number">
        </mat-form-field>

        <mat-form-field appearance="outline" class="col">
          <mat-label>الضريبة (taxAmount) - مبلغ</mat-label>
          <input matInput formControlName="taxAmount" type="number">
        </mat-form-field>
      </div>

      <div class="row mt-8">
        <mat-form-field appearance="outline" class="col">
          <mat-label>الإجمالي الكلي (totalAmount)</mat-label>
          <input matInput [value]="invoiceForm.get('totalAmount')?.value | number:'1.2-2'"  readonly>
        </mat-form-field>

        <mat-form-field appearance="outline" class="col">
          <mat-label>المدفوع (paidAmount)</mat-label>
          <input matInput formControlName="paidAmount" type="number">
        </mat-form-field>

        <mat-form-field appearance="outline" class="col">
          <mat-label>المتبقي (remainingAmount)</mat-label>
          <input matInput [value]="invoiceForm.get('remainingAmount')?.value | number:'1.2-2'" readonly>
        </mat-form-field>
      </div>

      <div class="actions mt-12">
        <button mat-raised-button color="accent" type="submit">حفظ الفاتورة</button>
      </div>
    </mat-card-content>
  </mat-card>
</form>

</div>
