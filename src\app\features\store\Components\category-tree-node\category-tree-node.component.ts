import { AfterViewInit, Component, EventEmitter, Input, OnChanges, Output, SimpleChanges, ViewChild } from '@angular/core';
import { Category } from '../../../../models/product.model';
import { MatTree } from '@angular/material/tree';
import { BehaviorSubject, Observable } from 'rxjs';


@Component({
  selector: 'app-category-tree-node',
  standalone: false,
  templateUrl: './category-tree-node.component.html',
  styleUrl: './category-tree-node.component.css'
})
export class CategoryTreeNodeComponent   implements AfterViewInit, OnChanges {
  @ViewChild('tree') tree!: MatTree<Category>;

  // Input: البيانات الأصلية للشجرة
  @Input() data: Category[] = [];

  // Input: النص الظاهر في حقل البحث
  @Input() placeholder: string = 'ابحث عن تصنيف...';

  // Output: عند اختيار تصنيف
  @Output() selected = new EventEmitter<number | number>();

  // حالة تحديد العنصر
  @Input() selectedCategoryId: number | null = null;

 


  // مصدر البيانات للشجرة
  dataSourceCategories = new BehaviorSubject<Category[]>([]);
  dataSource$: Observable<Category[]> = this.dataSourceCategories.asObservable();

  
  expandedNodes : boolean = false;

  // تحديد إذا كان للعقدة أبناء
  hasChild = (_: number, node: Category) => !!node.children && node.children.length > 0;

  // دالة لاسترجاع الأبناء
  childrenAccessor = (node: Category) => node.children || [];

  // نسخة من البيانات الأصلية لاستخدامها في التصفية
  private filteredData: Category[] = [];

  
  ngAfterViewInit() {
    this.initializeTree();

setTimeout(() => {
       if (this.selectedCategoryId) {
      this.expandToNode(this.selectedCategoryId, this.data);
    }
    }, 100);
   
   
  }
private expandToNode(categoryId: number, nodes: Category[]): boolean {

  for (const node of nodes) {
    if (node.id === categoryId) {
      return true; // العقدة نفسها
    }
     
    if (node.children && this.expandToNode(categoryId, node.children)) {
      // Expand الأب لو الابن موجود
     
      this.tree.expand(node);
      this.expandedNodes = true;
      return true;
    }
  }
      
  return false;
}




  ngOnChanges(changes: SimpleChanges): void {
    if (changes['data']) {
      this.filteredData = JSON.parse(JSON.stringify(this.data)); // نسخ عميقة
      this.dataSourceCategories.next(this.filteredData);
      
   
    }
  }

  initializeTree() {
    this.filteredData = JSON.parse(JSON.stringify(this.data));
    this.dataSourceCategories.next(this.filteredData);
   
  }

  selectCategory(id: number | number) {
    this.selectedCategoryId = id;
    this.selected.emit(id);
   
  }

  filterCategories(query: string) {
    if (!query.trim()) {
      this.dataSourceCategories.next(JSON.parse(JSON.stringify(this.data)));
      this.expandedNodes = false;
      return;
    }

    const filtered = this.filterNodes(this.data, query.toLowerCase());
    this.dataSourceCategories.next(filtered);
    this.expandedNodes = true;
    
   
  }

  private filterNodes(nodes: Category[], query: string): Category[] {
    return nodes
      .map(node => {
        const childMatches = this.filterNodes(node.children || [], query);
        if (node.name.toLowerCase().includes(query) || childMatches.length > 0) {
          
          return {
            ...node,
            children: childMatches
            
          };
        }
        return null;
      })
      .filter(Boolean) as Category[];
  }
}