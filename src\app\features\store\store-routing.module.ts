import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ProductDashboradComponent } from './Components/product-dashborad/product-dashborad.component';
import { UnitListComponent } from './Components/unit-list/unit-list.component';
import { CategoryListComponent } from './Components/category-list/category-list.component';
import { ProductListComponent } from './Components/product-list/product-list.component';
import { CreateInvoiceComponent } from './Components/create-invoice/create-invoice.component';

const routes: Routes = [{
    path: 'products-list',
    component: ProductListComponent,
    title: 'المنتجات',
  },{
    path: 'category-list',    
    component: CategoryListComponent,
    title: 'معاملات الشركاء',
  }
  ,{
    path: 'unit-list',
    component: UnitListComponent,
    title:'الواحدات',
  },{
    path: '',
    component: ProductDashboradComponent,
    title: 'لوحة تحكم المنتجات',
  
  },{
    path: 'create-invoice',
    component: CreateInvoiceComponent,
    title: 'فاتورة مبيعات',
  }];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class StoreRoutingModule { }
