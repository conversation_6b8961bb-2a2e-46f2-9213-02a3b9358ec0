import { Component, OnInit } from '@angular/core';
import { Category, Product, Children } from '../../../../models/product.model';
import { MatTreeNestedDataSource } from '@angular/material/tree';
import { ProductService } from '../../../../services/product.service';
import { PageEvent } from '@angular/material/paginator';
import { ImagePreviewDialogComponent } from '../../../shared/image-preview-dialog/image-preview-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import {
  trigger,
  state,
  style,
  animate,
  transition,
} from '@angular/animations';
import { ProductListDialogComponent } from '../product-list-dialog/product-list-dialog.component';
import { MatSnackBar } from '@angular/material/snack-bar';

@Component({
  selector: 'app-product-dashborad',
  standalone: false,
  templateUrl: './product-dashborad.component.html',
  styleUrl: './product-dashborad.component.css',
  animations: [
    trigger('fade', [
      state('void', style({ opacity: 0 })),
      transition('void <=> *', [animate('300ms ease-in-out')]),
    ]),
  ],
})
export class ProductDashboradComponent implements OnInit {
  searchText = '';

  viewMode: 'table' | 'card' = 'table';

  sidebarFilterCollapsed = false;
  Products: Product[] = [];

  ProductCard: Product[] = [];

  selectedCategoryId?: number;
  categories: Category[] = [];

  selectedStockStatus?: string;
  stockStatus: string | null = null;

  selectedItemType?: number | null = null;

  selectedType: number | null = null;

  totalCount = 0;
  pageSize = 10;
  pageNumber = 1;
  searchTerm = '';

  isActive?: boolean;
  loading = false;

  hasMore = true; // هل هناك صفحات تالية؟
  isAppending = false;

  displayedColumns: string[] = [
    'code',
    'barcode',
    'name',
    'categoryName',
    'balance',
    'standardCost',
    'imagePath',
    'actions',
  ];

  constructor(
    private productService: ProductService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {}

  dataSourceCategories = new MatTreeNestedDataSource<Category>();
  childrenAccessor = (node: Category) => node.children ?? [];

  hasChild = (_: number, node: Category) =>
    node.children && node.children.length > 0;

  toggleSidebarFilter() {
    this.sidebarFilterCollapsed = !this.sidebarFilterCollapsed;
  }

  ngOnInit(): void {
    this.loadCategories();
    this.loadMore();
    this.loadData();
  }
  loadData(): void {
    this.loading = true;
    this.productService
      .getProducts({
        pageNumber: this.pageNumber,
        pageSize: this.pageSize,
        searchTerm: this.searchTerm,
        isActive: this.isActive,
        categoryId: this.selectedCategoryId,
        itemType: this.selectedItemType,
        stockStatus: this.selectedStockStatus,
      })
      .subscribe({
        next: (response) => {
          if (response.succeeded && response.data) {
            this.Products = response.data.items;
            this.totalCount = response.data.totalCount;
          }
          this.loading = false;
        },
        error: (error) => {
          this.loading = false;
          this.Products = [];
        },
      });
  }

  loadMore(): void {
    if (this.loading || this.isAppending || !this.hasMore) return;

    this.isAppending = true;
    this.loading = true;

    this.productService
      .getProducts({
        pageNumber: this.pageNumber,
        pageSize: this.pageSize,
        searchTerm: this.searchTerm,
        isActive: this.isActive,
        categoryId: this.selectedCategoryId,
        itemType: this.selectedItemType,
        stockStatus: this.selectedStockStatus,
      })
      .subscribe({
        next: (response) => {
          if (response.succeeded && response.data) {
            const newItems = response.data.items || [];

            // أضف العناصر الجديدة
            this.ProductCard = [...this.ProductCard, ...newItems];

            // تحقق إذا كانت هذه آخر صفحة
            this.hasMore = this.ProductCard.length < response.data.totalCount;

            // إن لم تكن هناك عناصر، علشان ما يحاولش يحمل تاني
            if (newItems.length === 0) {
              this.hasMore = false;
            }
          }
          this.isAppending = false;
          this.loading = false;
        },
        error: (err) => {
          console.error('Error loading products', err);
          this.isAppending = false;
          this.loading = false;
        },
      });
  }

  onScrolledIndexChange(index: number): void {
    // عندما نصل إلى آخر 3 عناصر تقريبًا
    const endThreshold = 3;
    const itemsLoaded = this.ProductCard.length;
    const isNearEnd = index >= itemsLoaded - this.pageSize;

    if (isNearEnd && !this.loading && this.hasMore) {
      this.pageNumber++; // الانتقال للصفحة التالية
      this.loadMore();
    }
  }

  // نسخة أصلية من التصنيفات
  allCategories: Category[] = [];

  // تصفية الشجرة
  filterCategories(searchTerm: string): void {
    if (!searchTerm.trim()) {
      this.dataSourceCategories.data = this.allCategories;
      return;
    }

    const filtered = this.allCategories
      .map((cat) => this.filterNode(cat, searchTerm.trim().toLowerCase()))
      .filter((node) => node !== null) as Category[];

    this.dataSourceCategories.data = filtered;
  }

  getStatusText(balance: number, minimumStock: number): string {
    if (balance === 0) return 'غير متوفر';
    if (balance <= minimumStock) return 'كمية قليلة';
    return 'متوفر';
  }

  filterNode(node: Category, term: string): Category | null {
    const matches = node.name.toLowerCase().includes(term);

    const filteredChildren = (
      node.children?.map((child) => this.filterNode(child, term)) || []
    ).filter((child) => child !== null) as Category[];

    // Convert Category[] to Children[] by ensuring parentCategoryId is not null
    const childrenArray: Children[] = filteredChildren.map((child) => ({
      ...child,
      parentCategoryId: child.parentCategoryId ?? -1, // Convert null to -1
      children: child.children || [], // Ensure children is not undefined
    }));

    if (matches || filteredChildren.length > 0) {
      // نعيد الكائن مع تحويل parentCategoryId إلى عدد (أو -1 إذا كان null)
      return {
        ...node,
        parentCategoryId: node.parentCategoryId ?? -1, // أو 0 إذا لم يكن -1 مناسبًا
        children: childrenArray,
      };
    }
    return null;
  }

  loadCategories(): void {
    this.productService.getCategories().subscribe({
      next: (response) => {
        if (response.succeeded && response.data) {
          this.allCategories = response.data;
          this.dataSourceCategories.data = response.data;
        }
      },
      error: (error) => {
        console.error('Error loading categories:', error);
      },
    });
  }

  onPageChange(event: PageEvent): void {
    this.pageNumber = event.pageIndex + 1;
    this.pageSize = event.pageSize;
    this.loadData();
    this.loadMore();
  }

  onSearch(): void {
    this.pageNumber = 1;
    this.hasMore = true;
    this.ProductCard = [];
    this.loadMore();
    this.loadData();
  }

  onFilterChange(): void {
    this.pageNumber = 1;
    this.hasMore = true;
    this.ProductCard = [];
    this.loadMore();
    this.loadData();
  }

  clearFilters(): void {
    this.searchTerm = '';
    this.selectedCategoryId = undefined;
    this.selectedStockStatus = undefined;
    this.searchText = '';
    this.selectedItemType = null;
    this.filterCategories('');
    this.pageNumber = 1;
    this.loadData();
    this.hasMore = true;
    this.ProductCard = [];
    this.loadMore();
  }

  filterByItemType(itemType: number | null): void {
    this.selectedType = itemType;
    this.selectedItemType = itemType;
    this.onFilterChange();
  }

  createProduct(): void {
    const dialogRef = this.dialog.open(ProductListDialogComponent, {
      width: '800px',
      disableClose: true,
      data: { mode: 'create' },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.loadData();
        this.snackBar.open('تم إنشاء الصنف بنجاح', 'إغلاق', { duration: 3000 });
      }
    });
  }

  editProduct(product: Product) {
    const dialogRef = this.dialog.open(ProductListDialogComponent, {
      width: '800px',
      disableClose: true,
      data: { mode: 'edit', data: product },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.loadData();
        this.snackBar.open('تم تحديث الصنف بنجاح', 'إغلاق', { duration: 3000 });
      }
    });
  }

  deleteProduct(product: Product): void {
    if (confirm(`هل أنت متأكد من حذف  "${product.name}"؟`)) {
      this.productService.deleteProduct(product.id).subscribe({
        next: (response) => {
          if (response.succeeded && response.data != null) {
            this.loadMore();
            this.loadData();
            this.snackBar.open('تم حذف بنجاح', 'إغلاق', { duration: 3000 });
          }
        },
        error: (error) => {
          this.snackBar.open('حدث خطأ أثناء حذف الصنف', 'إغلاق', {
            duration: 3000,
          });
        },
      });
    }
  }

  exportToViews() {}

  selectCategory(categoryId: number) {
    this.selectedCategoryId = categoryId;
    this.onFilterChange();
  }

  openImagePreview(imageUrl: string): void {
    this.dialog.open(ImagePreviewDialogComponent, {
      width: 'auto',
      height: 'auto',
      maxWidth: '95vw',
      maxHeight: '95vh',
      data: {
        imageUrl: imageUrl,
        imageAlt: 'صورة المنتج',
      },
    });
  }


  

}
