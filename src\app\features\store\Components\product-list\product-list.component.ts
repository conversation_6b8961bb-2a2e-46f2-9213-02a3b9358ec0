import { Component, OnInit } from '@angular/core';
import { PageEvent } from '@angular/material/paginator';
import { Category, Children, Product } from '../../../../models/product.model';
import { MatTreeNestedDataSource } from '@angular/material/tree';
import { ProductService } from '../../../../services/product.service';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

@Component({
  selector: 'app-product-list',
  standalone: false,
  templateUrl: './product-list.component.html',
  styleUrl: './product-list.component.css'
})
export class ProductListComponent implements OnInit {


 dataSourceCategories = new MatTreeNestedDataSource<Category>();
    childrenAccessor = (node: Category) => node.children ?? [];


    hasChild = (_: number, node: Category) => node.children && node.children.length > 0;
    allCategories: Category[] = [];

    expandedNodes : boolean = false;

 Products: Product[] = [];  

 selectedCategoryId?: number;
 categories: Category[] = [];  
 
selectedStockStatus?: string;
 stockStatus: string | null = null; 

  totalCount = 0;
  pageSize = 10;
  pageNumber = 1;
  searchTerm: string = '';

  isActive?: boolean; 
  loading = false;

 displayedColumns: string[] = [ 'code', 'barcode',  'name', 'categoryName' ,'balance', 'actions'];


  constructor(private productService: ProductService,private sanitizer: DomSanitizer) {
  }

  ngOnInit(): void {
    this.loadCategories();
    this.loadData();
  }

loadCategories(): void {
  this.productService.getCategories().subscribe({
    next: (response) => {
      if (response.succeeded && response.data) {
        this.allCategories = response.data;
        this.dataSourceCategories.data = response.data;
      }
    },
    error: (error) => {
      console.error('Error loading categories:', error);
    }
  });  
}


loadData() : void{

    this.loading = true;
    this.productService.getProducts(
      {
        pageNumber: this.pageNumber,
        pageSize: this.pageSize,
        searchTerm: this.searchTerm,
        isActive: this.isActive,
        categoryId: this.selectedCategoryId,
        stockStatus: this.selectedStockStatus
      },
 
    ).subscribe({
      next: (response) => {
        if (response.succeeded && response.data) {
          this.Products = response.data.items;
          this.totalCount = response.data.totalCount;
        }
        this.loading = false;
      },
      error: (error) => {
         this.loading = false;
          this.Products = [];
      }
    });
  }

onPageChange(event: PageEvent): void {
    this.pageNumber = event.pageIndex + 1;
    this.pageSize = event.pageSize;
    this.loadData();
  }

  onSearch(): void {
    this.pageNumber = 1;
    this.loadData();
  }

  onFilterChange(): void {
    this.pageNumber = 1;
    this.loadData();
  }



 clearFilters(): void {
    this.searchTerm = '';
    this.selectedCategoryId = undefined;
    this.selectedStockStatus = undefined;
   this.pageNumber = 1;
    this.loadData();
  }



createProduct() { 
}

editProduct(product: Product) { 
}

deleteProduct(product: Product) { 
}

exportToViews () { 
}

selectCategory(categoryId: number) {
  this.selectedCategoryId = categoryId;
  this.onFilterChange();

}



// تصفية الشجرة
filterCategories(searchTerm: string): void {
   this.searchTerm = searchTerm;  
  if (!searchTerm.trim()) {
    this.dataSourceCategories.data = this.allCategories;
    this.expandedNodes = false;
    return;
  }

  const filtered = this.allCategories
    .map(cat => this.filterNode(cat, searchTerm.trim().toLowerCase()))
    .filter(node => node !== null) as Category[];

  this.dataSourceCategories.data = filtered;
  this.expandedNodes = true;
}

filterNode(node: Category, term: string): Category | null {
  const matches = node.name.toLowerCase().includes(term);

  const filteredChildren = (node.children?.map(child => this.filterNode(child, term)) || [])
    .filter(child => child !== null) as Category[];

  // Convert Category[] to Children[] by ensuring parentCategoryId is not null
  const childrenArray: Children[] = filteredChildren.map(child => ({
    ...child,
    parentCategoryId: child.parentCategoryId ?? -1, // Convert null to -1
    children: child.children || [] // Ensure children is not undefined
  }));

  if (matches || filteredChildren.length > 0) {
    // نعيد الكائن مع تحويل parentCategoryId إلى عدد (أو -1 إذا كان null)
    return {
      ...node,
      parentCategoryId: node.parentCategoryId ?? -1, // أو 0 إذا لم يكن -1 مناسبًا
     children: childrenArray,
     hasMatch: matches,
     // children: filteredChildren
    };
  }
  return null;
}

 highlight(text: string, search: string): SafeHtml {
    if (!search) return text;
    const regex = new RegExp(`(${search})`, 'gi');
    const highlighted = text.replace(regex, `<span class="highlight">$1</span>`);
    return this.sanitizer.bypassSecurityTrustHtml(highlighted);
  }

get hasChildMatch(): (node: Category) => boolean {
  return (node: Category) => {
    return !node.hasMatch && node.children?.some(child => child.hasMatch) === true;
  };
}

selectedIds: number[] = [];

toggleSelection(node: any) {
  if (this.selectedIds.includes(node.id)) {
    // لو متعلم قبل كده نشيله
    this.selectedIds = this.selectedIds.filter(id => id !== node.id);
  } else {
    // لو مش متعلم نضيفه
    this.selectedIds.push(node.id);
  }
   console.log(this.selectedIds);
}

// Helper function: هل النود متعلم؟
isChecked(node: any): boolean {
 
  return this.selectedIds.includes(node.id);
}

}


