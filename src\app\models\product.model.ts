export interface Product {
  id: number 
  code: string
  barcode: string
  name: string
  description: string
  categoryId: number
  categoryName: string
  unitId: number
  unitName: string
  standardCost: number
  minimumStock: number
  maximumStock: number
  openingBalance: number
  balance: number
  sortOrder: number
  itemType: number
  ItemTypeName : string
  imagePath: string
  createdAt: string
  updatedAt: string
  createdBy: number
  updatedBy: number
  isDeleted: boolean
  isActive: boolean
}

export interface ProductReq { 
  categoryId: number | null;
  stockStatus: string | null;
  priceFrom: number | null;
  priceTo: number | null;
}


export interface CreateProductDto  {
    code: string;
    name: string;
    description: string;
    barcode: string;
    categoryId: number;
    unitId: number;
    standardCost: number;
    minimumStock: number | null;
    maximumStock: number | null;
    openingBalance: number;
    sortOrder: number | null;
    itemType: number | null;
    imagePath?: File | null; 
}

export interface UpdateProductDto {
  id: number;
  code: string;
  name: string;
  description: string;
  barcode: string;
  categoryId: number;
  unitId: number;
  standardCost: number;
  minimumStock: number | null;
  maximumStock: number | null;
  openingBalance: number;
  sortOrder: number | null;
  itemType: number | null;
  imagePath?: File | null; 
  removeImage: boolean | false;
}

export class ItemType {
  id: number = 1;
  name?: string;
}


export interface Category {
  parentCategoryId: number | null;
  parentCategoryName: string
  code: string
  symbol: string
  categoryTypeId: number
  imageUrl: any
  sortOrder: number
  level: number
  hasChildren: boolean
  children?: Children[]
  name: string
  description: string
  id: number
  createdAt: string
  updatedAt: any
  createdBy: number
  updatedBy: any
  isDeleted: boolean
  isActive: boolean
  hasMatch?: boolean
}

export interface Children {
  parentCategoryId: number
  parentCategoryName: string
  code: string
  symbol: string
  categoryTypeId: number
  imageUrl: any
  sortOrder: number
  level: number
  hasChildren: boolean
  children: any[]
  name: string
  description: string
  id: number
  createdAt: string
  updatedAt: any
  createdBy: number
  updatedBy: any
  isDeleted: boolean
  isActive: boolean
  hasMatch?: boolean
}

export interface UnitOfMeasure {
  id: number
  name: string
  description: string
  symbol?: string
  createdAt: string
  updatedAt: any
  createdBy: number
  updatedBy: any
  isDeleted: boolean
  isActive: boolean
}

export interface CreateInvoice {
  id: number;
  createdAt: string;
  createdBy: number;
  isActive: boolean;
  invoiceNumber: string;
  invoiceType: number;
  invoiceDate: string;
  partyId: number;
  subTotal: number;
  itemDiscountAmount: number;
  invoiceDiscountAmount: number;
  taxAmount: number;
  totalAmount: number;
  paidAmount: number;
  remainingAmount: number;
  paymentType: number;
  isPaid: boolean;
  notes: string;
  items: InvoiceItem[];
}

export interface InvoiceItem {
  id: number;
  createdAt: string;
  updatedAt: string;
  createdBy: number;
  updatedBy: number;
  isDeleted: boolean;
  isActive: boolean;
  invoiceMasterId: number;
  productId: number;
  quantity: number;
  unitPrice: number;
  discountPercentage: number;
  discountAmount: number;
  totalPrice: number;
  notes: string;
}