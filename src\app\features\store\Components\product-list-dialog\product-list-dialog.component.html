<div class="dialog-container">
    <div mat-dialog-title class="dialog-header">
        <mat-icon>{{ isEditMode ? 'edit' : 'person_add' }}</mat-icon>
        <h2>{{ isEditMode ? 'تعديل صنف جديد' : 'إضافة صنف جديد' }}</h2>
    </div>

    <div mat-dialog-content class="dialog-content">
        <form [formGroup]="form" (ngSubmit)="onSubmit()">



            <!-- ItemType -->
            <div class="form-row">
                <mat-label>نوع</mat-label>
                <br>
                <mat-radio-group aria-label="Select an option" formControlName="itemType" required>

                    <mat-radio-button *ngFor="let rep of itemType" [value]="rep.id">
                        {{ rep.name }}
                    </mat-radio-button>
                    <mat-error *ngIf="form.get('itemType')?.hasError('required')">
                        نوع الصنف مطلوب
                    </mat-error>

                </mat-radio-group>
            </div>


            <!-- Category Tree -->
            <div class="form-row">
               <app-category-tree-node [data]="categories" 
                    [selectedCategoryId]="form.get('categoryId')?.value"
                    (selected)="onCategorySelected($event)"></app-category-tree-node>
                <mat-error *ngIf="form.get('categoryId')?.hasError('required')">
                    التصنيف مطلوب
                </mat-error>
            </div>
                      

            <!-- UnitOfMeasure -->
            <div class="form-row unitOf">
                <mat-form-field class="form-field">
                    <mat-label>الوحدة</mat-label>
                    <mat-select formControlName="unitId" required>
                        <mat-option *ngFor="let rep of unitOfMeasures" [value]="rep.id">
                            {{ rep.description }} {{ rep.symbol }}
                        </mat-option>
                    </mat-select>
                    <mat-error *ngIf="form.get('unitId')?.hasError('required')">
                        الوحدة مطلوبة
                    </mat-error>
                </mat-form-field>
            </div>

            <!-- Code & Barcode  -->
            <div class="form-row two-fields">
                <mat-form-field class="form-field">
                    <mat-label>الكود</mat-label>
                    <input matInput formControlName="code" placeholder="الكود">
                    <mat-icon matSuffix>code</mat-icon>
                    <mat-error *ngIf="form.get('code')?.invalid && form.get('code')?.touched">
                        {{ getErrorMessage('code') }}
                    </mat-error>
                </mat-form-field>


                <!-- Barcode -->
                <mat-form-field class="form-field">
                    <mat-label>الباركود</mat-label>
                    <input matInput formControlName="barcode" placeholder="الباركود">
                    <mat-icon matSuffix>qr_code</mat-icon>
                    <mat-error *ngIf="form.get('barcode')?.invalid && form.get('barcode')?.touched">
                        {{ getErrorMessage('barcode') }}
                    </mat-error>
                </mat-form-field>

            </div>

            <!-- Name -->
            <div class="form-row">
                <mat-form-field class="full-width">
                    <mat-label>الاسم</mat-label>
                    <input matInput formControlName="name" placeholder="الاسم">
                    <mat-icon matSuffix>label</mat-icon>
                    <mat-error *ngIf="form.get('name')?.invalid && form.get('name')?.touched">
                        {{ getErrorMessage('name') }}
                    </mat-error>
                </mat-form-field>
            </div>

            <!-- Description -->
            <div class="form-row">
                <mat-form-field class="full-width">
                    <mat-label>الوصف</mat-label>
                    <input matInput formControlName="description" placeholder="الوصف">
                    <mat-icon matSuffix>description</mat-icon>
                    <mat-error *ngIf="form.get('description')?.invalid && form.get('description')?.touched">
                        {{ getErrorMessage('description') }}
                    </mat-error>
                </mat-form-field>
            </div>

            <!-- StandardCost & OpeningBalance -->
            <div class="form-row two-fields">
                <mat-form-field class="form-field">
                    <mat-label>التكلفة</mat-label>
                    <input matInput type="number" step="0.01" min="0" formControlName="standardCost"
                        placeholder="أدخل التكلفة">
                    <mat-icon matSuffix>money</mat-icon>
                    <mat-error *ngIf="form.get('standardCost')?.invalid && form.get('standardCost')?.touched">
                        {{ getErrorMessage('standardCost') }}
                    </mat-error>
                </mat-form-field>

                <mat-form-field class="form-field">
                    <mat-label>الرصيد الافتتاحي</mat-label>
                    <input matInput type="number" step="1.00" min="0" formControlName="openingBalance"
                        placeholder="أدخل الرصيد الافتتاحي">
                    <mat-icon matSuffix>account_balance_wallet</mat-icon>
                    <mat-error *ngIf="form.get('openingBalance')?.invalid && form.get('openingBalance')?.touched">
                        {{ getErrorMessage('openingBalance') }}
                    </mat-error>
                </mat-form-field>
            </div>

            <!-- MinimumStock & MaximumStock -->
            <div class="form-row two-fields">
                <mat-form-field class="form-field">
                    <mat-label>الحد الأدنى</mat-label>
                    <input matInput type="number" step="1.00" min="0" formControlName="minimumStock"
                        placeholder="أدخل الحد الأدنى">
                    <mat-icon matSuffix>low_priority</mat-icon>
                    <mat-error *ngIf="form.get('minimumStock')?.invalid && form.get('minimumStock')?.touched">
                        {{ getErrorMessage('minimumStock') }}
                    </mat-error>

                </mat-form-field>

                <mat-form-field class="form-field">
                    <mat-label>الحد الأقصى</mat-label>
                    <input matInput type="number" step="1.00" min="0" formControlName="maximumStock"
                        placeholder="أدخل الحد الأقصى">
                    <mat-icon matSuffix>high_priority</mat-icon>
                    <mat-error *ngIf="form.get('maximumStock')?.invalid && form.get('maximumStock')?.touched">
                        {{ getErrorMessage('maximumStock') }}
                    </mat-error>

                </mat-form-field>
            </div>

            <!-- Range Error Message -->
            <div *ngIf="form.hasError('rangeError') && 
               (form.get('minimumStock')?.touched || form.get('maximumStock')?.touched)" class="error-message">
                <mat-error>
                    يجب أن يكون الحد الأدنى أقل من أو يساوي الحد الأقصى
                </mat-error>
            </div>

            <!-- SortOrder -->
            <div class="form-row">
                <mat-form-field class="form-field adjust-width">
                    <mat-label>الترتيب</mat-label>
                    <input matInput type="number" step="1" min="0" formControlName="sortOrder" type="number"
                        placeholder="أدخل الترتيب">
                    <mat-icon matSuffix>sort</mat-icon>
                    <mat-error *ngIf="form.get('sortOrder')?.invalid && form.get('sortOrder')?.touched">
                        {{ getErrorMessage('sortOrder') }}
                    </mat-error>
                </mat-form-field>
            </div>


            <!-- Image Section -->
            <mat-card class="image-card">
                <mat-card-header>
                    <mat-card-title>
                        <mat-icon>photo_camera</mat-icon>
                        الصورة
                    </mat-card-title>

                </mat-card-header>

                <mat-card-content>
                    <!-- عرض الصورة الحالية أو المعاينة بعد الحفظ -->
                    <!-- اختيار الصورة -->
                    <div class="upload-section">
                        <input type="file" (change)="onFileSelected($event)" accept="image/*" class="file-input" />

                        <!-- معاينة فورية بعد الاختيار -->
                        <div *ngIf="imagePreview" class="image-preview-inline">
                            <img [src]="imagePreview" alt="معاينة الصورة" class="preview-thumb">
                            <button mat-icon-button color="warn" (click)="removeImage()" aria-label="حذف الصورة" type="button" matTooltip="حذف الصورة" >
                                <mat-icon>close</mat-icon>
                            </button>
                        </div>
                    </div>


                    <div class="image-display" *ngIf="imagePathUrl">
                        <div class="image-wrapper">
                            <img [src]="imagePathUrl" alt="الصورة النهائية" class="display-image">
                            <button mat-icon-button color="warn" class="remove-overlay-btn" (click)="removeCurrentImage()"
                                matTooltip="حذف الصورة" matTooltipPosition="above" type="button"
                                aria-label="حذف الصورة">
                                <mat-icon>delete</mat-icon>
                            </button>
                        </div>
                    </div>
                </mat-card-content>
            </mat-card>


        </form>
    </div>

    <div mat-dialog-actions class="dialog-actions">
        <button mat-button (click)="onCancel()" [disabled]="loading">
            إلغاء
        </button>
        <button mat-raised-button color="primary" (click)="onSubmit()" [disabled]="form.invalid || loading">
            <mat-spinner *ngIf="loading" diameter="20"></mat-spinner>
            <mat-icon *ngIf="!loading">{{ isEditMode ? 'save' : 'add' }}</mat-icon>
            {{ isEditMode ? 'حفظ التغييرات' : 'إضافة الصنف' }}
        </button>
    </div>
</div>