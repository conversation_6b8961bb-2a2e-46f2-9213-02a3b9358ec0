 <!-- Categories Tree -->
    <div class="categories-section">
      <div class="tree-header">
        <mat-label>التصنيفات</mat-label>
        <mat-form-field appearance="outline" class="tree-search-field">
          <input matInput placeholder="ابحث عن تصنيف..." #searchInput (input)="filterCategories(searchInput.value)">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>
      </div>

      <mat-tree #tree [dataSource]="dataSourceCategories"  [childrenAccessor]="childrenAccessor"   class="category-tree">

        <!-- Leaf Node -->
        <mat-tree-node *matTreeNodeDef="let node" class="tree-node" >
          <span class="node-content" (click)="selectCategory(node.id) "
            [class.selected]="selectedCategoryId === node.id">
            <mat-icon class="icon">label_outline</mat-icon>
            <span class="node-name">{{ node.name }}</span>
          </span>
        </mat-tree-node>

        <!-- Expandable Node -->
        <mat-tree-node *matTreeNodeDef="let node; when: hasChild"  matTreeNodePadding
                 isExpandable    
                 [isExpanded]="expandedNodes"  class="tree-node expandable">
         
          <button mat-icon-button matTreeNodeToggle class="toggle-btn" type="button">
            <mat-icon>
              {{ tree.isExpanded(node) ? 'expand_more' : 'chevron_right' }}
            </mat-icon>
          </button>
          <span class="node-content" (click)="selectCategory(node.id)"
            [class.selected]="selectedCategoryId === node.id">
            <mat-icon class="folder-icon">folder_open</mat-icon>
            <span class="node-name">{{ node.name }}</span>
          </span>
        </mat-tree-node>
      </mat-tree>
    </div>
