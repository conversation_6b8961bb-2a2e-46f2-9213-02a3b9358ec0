import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { StoreRoutingModule } from './store-routing.module';
import { ProductDashboradComponent } from './Components/product-dashborad/product-dashborad.component';
import { UnitListComponent } from './Components/unit-list/unit-list.component';
import { CategoryListComponent } from './Components/category-list/category-list.component';
import { ProductListComponent } from './Components/product-list/product-list.component';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatDialogModule } from '@angular/material/dialog';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatCheckbox } from "@angular/material/checkbox";
import { MatInputModule } from "@angular/material/input";
import { ReactiveFormsModule } from '@angular/forms';
import { MatSortModule } from '@angular/material/sort';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { FormsModule } from '@angular/forms';
import { MatRadioModule } from '@angular/material/radio';
import { SharedModule } from '../../shared/shared.module';
import { NgChartsModule } from 'ng2-charts';
import { MatTreeModule } from '@angular/material/tree';   
import {MatSidenavModule} from '@angular/material/sidenav';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { ProductListDialogComponent } from './Components/product-list-dialog/product-list-dialog.component';
import { MatMenuModule } from '@angular/material/menu';
import { CategoryTreeNodeComponent } from './Components/category-tree-node/category-tree-node.component';
import { ScrollingModule } from '@angular/cdk/scrolling';
import { NgxSkeletonLoaderModule } from 'ngx-skeleton-loader';
import { CreateInvoiceComponent } from './Components/create-invoice/create-invoice.component';
import {MatAutocompleteModule} from '@angular/material/autocomplete';



@NgModule({
  declarations: [
    ProductDashboradComponent,
    UnitListComponent,
    CategoryListComponent,
    ProductListComponent,
    ProductListDialogComponent,
    CategoryTreeNodeComponent,
    CreateInvoiceComponent
  ],
  imports: [
    CommonModule,
    StoreRoutingModule,
   MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatDialogModule,
    MatTooltipModule,
    MatCheckbox,
    MatInputModule,
    ReactiveFormsModule,
    MatSortModule,
    MatPaginatorModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    FormsModule,
    MatRadioModule,
    MatTreeModule,
    SharedModule,
    MatSidenavModule,
    MatButtonToggleModule,
    MatMenuModule,
    ScrollingModule,
    NgxSkeletonLoaderModule,
    MatAutocompleteModule,
    NgChartsModule
     
  ]
})
export class StoreModule { }
