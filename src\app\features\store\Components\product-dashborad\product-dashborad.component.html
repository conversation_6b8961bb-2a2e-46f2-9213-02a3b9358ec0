<mat-drawer-container class="drawer-container" autosize>


  <!-- Sidebar Drawer -->
  <mat-drawer *ngIf="!sidebarFilterCollapsed" mode="over" opened position="start" class="filters-drawer">

    <button mat-icon-button (click)="toggleSidebarFilter()">
      <mat-icon>menu</mat-icon>
    </button>

    <!-- Drawer Header -->
    <div class="drawer-header">
      <h3>تصفية المنتجات</h3>
      <button mat-icon-button (click)="clearFilters()">
        <mat-icon>clear_all</mat-icon>
      </button>
    </div>

    <!-- Search Field -->
    <mat-form-field class="full-width" appearance="outline">
      <mat-label>البحث</mat-label>
      <input matInput [(ngModel)]="searchTerm" (keyup.enter)="onSearch()" placeholder="ابحث بالاسم، الكود أو الباركود">
      <mat-icon matSuffix>search</mat-icon>
    </mat-form-field>

    <!-- Stock Status Filter -->
    <mat-form-field class="full-width" appearance="outline">
      <mat-label>حالة المخزون</mat-label>
      <mat-select [(ngModel)]="selectedStockStatus" (selectionChange)="onFilterChange()">
        <mat-option value="">جميع الحالات</mat-option>
        <mat-option [value]="'instock'">متوفر</mat-option>
        <mat-option [value]="'lowstock'">كمية قليلة</mat-option>
        <mat-option [value]="'outofstock'">غير متوفر</mat-option>
      </mat-select>
    </mat-form-field>

    <!-- Categories Tree -->
    <div class="categories-section">
      <div class="tree-header">
        <mat-label>التصنيفات</mat-label>
        <mat-form-field appearance="outline" class="tree-search-field">
          <input matInput placeholder="ابحث عن تصنيف..." #searchInput (input)="filterCategories(searchInput.value)">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>
      </div>

      <mat-tree #tree [dataSource]="dataSourceCategories" [childrenAccessor]="childrenAccessor" class="category-tree">

        <!-- Leaf Node -->
        <mat-tree-node *matTreeNodeDef="let node" class="tree-node">
          <span class="node-content" (click)="selectCategory(node.id)"
            [class.selected]="selectedCategoryId === node.id">
            <mat-icon class="icon">label_outline</mat-icon>
            <span class="node-name">{{ node.name }}</span>
          </span>
        </mat-tree-node>

        <!-- Expandable Node -->
        <mat-tree-node *matTreeNodeDef="let node; when: hasChild" class="tree-node expandable">
          <button mat-icon-button matTreeNodeToggle class="toggle-btn">
            <mat-icon>
              {{ tree.isExpanded(node) ? 'expand_more' : 'chevron_right' }}
            </mat-icon>
          </button>
          <span class="node-content" (click)="selectCategory(node.id)"
            [class.selected]="selectedCategoryId === node.id">
            <mat-icon class="folder-icon">folder_open</mat-icon>
            <span class="node-name">{{ node.name }}</span>
          </span>
        </mat-tree-node>
      </mat-tree>
    </div>

    <!-- Apply Filters Button -->
    <div class="drawer-actions">
      <button mat-raised-button color="primary" (click)="onSearch()" class="full-width">
        <mat-icon>search</mat-icon> تطبيق الفلاتر
      </button>
      <button mat-button (click)="clearFilters()" class="full-width">
        <mat-icon>clear</mat-icon> مسح الفلاتر
      </button>
    </div>

  </mat-drawer>

  <!-- Products Section -->

  <!-- Main Content -->
  <mat-drawer-content class="content">

    <!-- Header -->
    <div class="page-header">

      <h1 class="page-title">
        <mat-icon>receipt_long</mat-icon>
        إدارة الأصناف والمنتجات
      </h1>

      <div class="header-actions">

        <div *appHasPermission="['Permissions.Stores.Add']">
          <button mat-raised-button color="primary" (click)="createProduct()" class="action-btn">
            <mat-icon>add</mat-icon> إنشاء صنف
          </button>
        </div>
      </div>
    </div>
    <div class="header-export">
      <button mat-icon-button (click)="toggleSidebarFilter()">
        <mat-icon>filter_list</mat-icon>
      </button>

      <button mat-raised-button color="accent" (click)="exportToViews()" class="action-btn">
        <mat-icon>picture_as_pdf</mat-icon> PDF
      </button>
    </div>


    <mat-button-toggle-group class="type-filters" [(ngModel)]="selectedType" [ngModelOptions]="{ standalone: true }"
      (change)="filterByItemType($event.value)">
      <mat-button-toggle [value]="null">
        <mat-icon>all_inclusive</mat-icon>
        <span>الكل</span>
      </mat-button-toggle>

      <mat-button-toggle [value]="1">
        <mat-icon>category</mat-icon>
        <span>أصناف</span>
      </mat-button-toggle>

      <mat-button-toggle [value]="2">
        <mat-icon>construction</mat-icon>
        <span>خامات</span>
      </mat-button-toggle>

      <mat-button-toggle [value]="3">
        <mat-icon>inventory_2</mat-icon>
        <span>منتجات</span>
      </mat-button-toggle>
    </mat-button-toggle-group>



    <div class="view-toggle">
      <mat-button-toggle-group [(ngModel)]="viewMode" aria-label="عرض المنتجات">
        <mat-button-toggle value="table" aria-label="عرض كجدول">
          <mat-icon>table_view</mat-icon>
        </mat-button-toggle>
        <mat-button-toggle value="card" aria-label="عرض كبطاقات">
          <mat-icon>grid_view</mat-icon>
        </mat-button-toggle>
      </mat-button-toggle-group>
    </div>

    <!-- Loading Spinner -->
    <div *ngIf="loading" class="loading-container">
      <mat-spinner></mat-spinner>
    </div>

    <!-- Product Table -->
    <div *ngIf="!loading && viewMode === 'table'" @fade class="table-wrapper">
      <mat-card class="table-card">

        <mat-card-content>
          <div class="table-container">
            <table mat-table [dataSource]="Products" class="products-table">

              <!-- Code -->
              <ng-container matColumnDef="code">
                <th mat-header-cell *matHeaderCellDef>كود الصنف</th>
                <td mat-cell *matCellDef="let prod">{{ prod.code || '-' }}</td>
              </ng-container>

              <!-- Barcode -->
              <ng-container matColumnDef="barcode">
                <th mat-header-cell *matHeaderCellDef>الباركود</th>
                <td mat-cell *matCellDef="let prod">{{ prod.barcode || '-' }}</td>
              </ng-container>

              <!-- Name -->
              <ng-container matColumnDef="name">
                <th mat-header-cell *matHeaderCellDef>الاسم</th>
                <td mat-cell *matCellDef="let prod">
                  <strong>{{ prod.name }}</strong>
                  <p class="desc" *ngIf="prod.description">{{ prod.description }}</p>
                </td>
              </ng-container>

              <!-- Category -->
              <ng-container matColumnDef="categoryName">
                <th mat-header-cell *matHeaderCellDef>التصنيف</th>
                <td mat-cell *matCellDef="let prod">{{ prod.categoryName || 'عام' }}
                  <br />
                  <span class="type">نوعه: {{ prod.itemTypeName }}</span>
                </td>
              </ng-container>

              <!-- Balance & Stock Status -->
              <ng-container matColumnDef="balance">
                <th mat-header-cell *matHeaderCellDef>الرصيد</th>
                <td mat-cell *matCellDef="let prod">
                  <strong>{{ prod.balance || 0 }} {{ prod.unitName }}</strong>
                  <br />
                  <span class="status" [ngClass]="{
                            'in-stock': prod.balance > prod.minimumStock,
                            'low-stock': prod.balance > 0 && prod.balance <= prod.minimumStock,
                            'out-of-stock': prod.balance === 0
                          }">
                    {{ getStatusText(prod.balance, prod.minimumStock) }}
                  </span>
                </td>
              </ng-container>

              <ng-component matColumnDef="standardCost">
                <th mat-header-cell *matHeaderCellDef>التكلفة</th>
                <td mat-cell *matCellDef="let prod"><strong>{{ prod.standardCost || 0 }}</strong></td>
              </ng-component>

              <!-- Image -->
              <ng-container matColumnDef="imagePath">
                <th mat-header-cell *matHeaderCellDef>الصورة</th>
                <td mat-cell *matCellDef="let sale">
                  <div class="image-container">
                    <img [src]="sale.imagePath" alt="صورة المنتج" class="image-product"
                      (click)="openImagePreview(sale.imagePath)" *ngIf="sale.imagePath" style="cursor: pointer;" />
                    <p *ngIf="!sale.imagePath">لا توجد صورة</p>
                  </div>
                </td>
              </ng-container>


              <!-- Actions -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>الإجراءات</th>
                <td mat-cell *matCellDef="let prod" class="actions-cell">
                  <div *appHasPermission="['Permissions.Stores.Edit']">
                    <button mat-icon-button color="primary" (click)="editProduct(prod)" matTooltip="تعديل">
                      <mat-icon>edit</mat-icon>
                    </button>
                  </div>
                  <div *appHasPermission="['Permissions.Stores.Delete']">
                    <button mat-icon-button color="warn" (click)="deleteProduct(prod)" matTooltip="حذف">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </div>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>

            <!-- No Data -->
            <div *ngIf="Products.length === 0" class="no-data">
              <mat-icon color="disabled">inbox</mat-icon>
              <p>لا توجد منتجات مطابقة للبحث</p>
            </div>
          </div>

          <!-- Pagination -->
          <mat-paginator *ngIf="totalCount > 0" [length]="totalCount" [pageSize]="pageSize"
            [pageSizeOptions]="[5, 10, 25, 50]" [pageIndex]="pageNumber - 1" (page)="onPageChange($event)"
            showFirstLastButtons>
          </mat-paginator>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Card View -->
    <!-- <div *ngIf="!loading && viewMode === 'card'" @fade class="cards-container">
      <div class="cards-grid">
        <mat-card *ngFor="let prod of Products" class="product-card">

          Product Image (if exists) 
          <div class="card-image">
            <img [src]="prod.imagePath || '/image/no-image.jpg'" [alt]="prod.name"
              (click)="openImagePreview(prod.imagePath)" class="product-image" style="cursor: pointer;">
          </div>

          Product Info 
          <mat-card-content class="card-content">
            <h3>{{ prod.name }}</h3>
            <p class="desc" *ngIf="prod.description">{{ prod.description }}</p>
            <p class="code">كود: {{ prod.code || '-' }}</p>
            <p class="barcode">باركود: {{ prod.barcode || '-' }}</p>
            <p class="category">التصنيف: {{ prod.categoryName || 'عام' }}</p>

            <div class="stock-status">
              <strong>الرصيد: {{ prod.balance || 0 }}</strong>
              <span class="status" [ngClass]="{
                  'in-stock': prod.balance > prod.minimumStock,
                  'low-stock': prod.balance > 0 && prod.balance <= prod.minimumStock,
                  'out-of-stock': prod.balance === 0
                }">
                {{ getStatusText(prod.balance, prod.minimumStock) }}
              </span>
            </div>
          </mat-card-content>

          !-- Actions
          <mat-card-actions class="card-actions">
            <div *appHasPermission="['Permissions.Stores.Edit']">
              <button mat-icon-button color="primary" (click)="editProduct(prod)" matTooltip="تعديل">
                <mat-icon>edit</mat-icon>
              </button>
            </div>
            <div *appHasPermission="['Permissions.Stores.Delete']">
              <button mat-icon-button color="warn" (click)="deleteProduct(prod)" matTooltip="حذف">
                <mat-icon>delete</mat-icon>
              </button>
            </div>
          </mat-card-actions>
        </mat-card>

         No Data in Card View 
        <div *ngIf="Products.length === 0" class="no-data">
          <mat-icon color="disabled">inventory</mat-icon>
          <p>لا توجد منتجات</p>
        </div>
      </div>
    </div> -->



     <ng-container *ngIf="loading">
      <cdk-virtual-scroll-viewport itemSize="200" class="virtual-scroll-viewport">
        <div class="cards-grid">
          <mat-card *cdkVirtualFor="let item of [1,2,3,4,5,6]" class="product-card">
            <div class="card-image">
              <ngx-skeleton-loader type="rect" height="150px"></ngx-skeleton-loader>
            </div>
            <mat-card-content>
              <ngx-skeleton-loader type="text" width="70%"></ngx-skeleton-loader>
              <ngx-skeleton-loader type="text" width="90%"></ngx-skeleton-loader>
            </mat-card-content>
          </mat-card>
        </div>
      </cdk-virtual-scroll-viewport>
     </ng-container> 
    <!-- Card View with Virtual Scroll -->
    <div *ngIf="!loading && viewMode === 'card'" @fade class="cards-container">

      <cdk-virtual-scroll-viewport itemSize="200" class="virtual-scroll-viewport"
        (scrolledIndexChange)="onScrolledIndexChange($event)">

        <div class="cards-grid">
          <mat-card *cdkVirtualFor="let prod of ProductCard" class="product-card">

            <!-- Product Image -->
            <div class="card-image">
              <img [src]="prod.imagePath || '/image/no-image.jpg'" [alt]="prod.name"
                (click)="openImagePreview(prod.imagePath)" class="product-image" style="cursor: pointer;">
            </div>

            <!-- Product Info -->
            <mat-card-content class="card-content">
              <h3>{{ prod.name }}</h3>
              <p class="desc" *ngIf="prod.description">{{ prod.description }}</p>
              <p class="code">كود: {{ prod.code || '-' }}</p>
              <p class="barcode">باركود: {{ prod.barcode || '-' }}</p>
              <p class="category">التصنيف: {{ prod.categoryName || 'عام' }}</p>

              <div class="stock-status">
                <strong>الرصيد: {{ prod.balance || 0 }}</strong>
                <span class="status" [ngClass]="{
                'in-stock': prod.balance > prod.minimumStock,
                'low-stock': prod.balance > 0 && prod.balance <= prod.minimumStock,
                'out-of-stock': prod.balance === 0
              }">
                  {{ getStatusText(prod.balance, prod.minimumStock) }}
                </span>
              </div>
            </mat-card-content>

            <!-- Actions -->
            <mat-card-actions class="card-actions">
              <div *appHasPermission="['Permissions.Stores.Edit']">
                <button mat-icon-button color="primary" (click)="editProduct(prod)" matTooltip="تعديل">
                  <mat-icon>edit</mat-icon>
                </button>
              </div>
              <div *appHasPermission="['Permissions.Stores.Delete']">
                <button mat-icon-button color="warn" (click)="deleteProduct(prod)" matTooltip="حذف">
                  <mat-icon>delete</mat-icon>
                </button>
              </div>
            </mat-card-actions>

              
         
         
         
          </mat-card>
        </div>

        <!-- شريط تحميل عند نهاية القائمة -->
        <div *ngIf="loading && hasMore" class="loading-more">
          <mat-spinner diameter="24"></mat-spinner>
          <span>جارٍ تحميل المزيد...</span>
        </div>

      </cdk-virtual-scroll-viewport>


    </div>




  </mat-drawer-content>

</mat-drawer-container>