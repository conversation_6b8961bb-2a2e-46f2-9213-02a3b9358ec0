<div class="partners-container">
  <!-- Header -->
  <div class="page-header">
    <h1 class="page-title">
      <mat-icon>receipt_long</mat-icon>
      إدارة حركات الشركاء
    </h1>

   <button mat-raised-button color="success" class="create-button" (click)="exportToViews()" >
        <mat-icon>picture_as_pdf</mat-icon>
        عرض PDF
     </button>
  <div *appHasPermission="['Permissions.Partner.Add']">
     <button mat-raised-button color="primary" class="create-button" (click)="createPartnerTransaction()">
      <mat-icon>add</mat-icon>
      إنشاء حركة جديدة
    </button>
    </div>
  </div>

  <!-- Filters -->
  <mat-card class="filters-card">
    <mat-card-content>
      <div class="filters-row">
        <mat-form-field class="search-field">
          <mat-label>البحث</mat-label>
          <input matInput
                 [(ngModel)]="searchTerm"
                 (keyup.enter)="onSearch()"
                 placeholder="البحث بالبيان او الحركة او الملاحظات">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>

        <mat-form-field class="filter-field">
          <mat-label>الشريك</mat-label>
          <mat-select [(ngModel)]="selectedPartnerId" (selectionChange)="onFilterChange()">
            <mat-option [value]="undefined">الكل</mat-option>
            <mat-option *ngFor="let rep of partners" [value]="rep.id">
              {{ rep.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>
       <div class="band-field">
          <mat-form-field class="filter-field">
            <mat-label>البند</mat-label>
            <mat-select [(ngModel)]="selectedbandId" (selectionChange)="onFilterChange()">
              <mat-option [value]="undefined">الكل</mat-option>
              <mat-option *ngFor="let rep of bands" [value]="rep.id">
                {{ rep.name }}
              </mat-option>
            </mat-select>
          </mat-form-field>       
        <a  routerLink="/partners/band-list"
                                color="primary">
                               <mat-icon>add</mat-icon>
        </a>
      </div>


        <mat-form-field class="date-field">
          <mat-label>من تاريخ</mat-label>
          <input matInput [matDatepicker]="fromPicker" [(ngModel)]="fromDate" (dateChange)="onFilterChange()">
          <mat-datepicker-toggle matSuffix [for]="fromPicker"></mat-datepicker-toggle>
          <mat-datepicker #fromPicker></mat-datepicker>
        </mat-form-field>

        <mat-form-field class="date-field">
          <mat-label>إلى تاريخ</mat-label>
          <input matInput [matDatepicker]="toPicker" [(ngModel)]="toDate" (dateChange)="onFilterChange()">
          <mat-datepicker-toggle matSuffix [for]="toPicker"></mat-datepicker-toggle>
          <mat-datepicker #toPicker></mat-datepicker>
        </mat-form-field>

        <div class="filter-actions">
          <button mat-raised-button color="primary" (click)="onSearch()">
            <mat-icon>search</mat-icon>
            بحث
          </button>
          <button mat-button (click)="clearFilters()">
            <mat-icon>clear</mat-icon>
            مسح الفلاتر
          </button>
        </div>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Loading Spinner -->
  <div *ngIf="loading" class="loading-container">
    <mat-spinner></mat-spinner>
  </div>

  <!-- partner Table -->
  <mat-card *ngIf="!loading" class="table-card">
    <mat-card-content>
      <div class="table-container">
        <table mat-table [dataSource]="partnerTransactions" class="partners-table">

          <!-- Invoice Number Column -->
          <ng-container matColumnDef="id">
            <th mat-header-cell *matHeaderCellDef>رقم الحركة</th>
            <td mat-cell *matCellDef="let sale">
              <div class="invoice-cell">
                <strong>{{ sale.id }}</strong>
                <div class="invoice-date">
                  {{ sale.transactionDate | date:'dd/MM/yyyy' }}
                </div>
              </div>
            </td>
          </ng-container>

          <!-- Invoice Date Column -->
          <ng-container matColumnDef="actionDetailName">
            <th mat-header-cell *matHeaderCellDef>الحركة</th>
            <td mat-cell *matCellDef="let sale">
              {{ sale.actionDetailName}}
            </td>
          </ng-container>

          <!-- Customer Column -->
          <ng-container matColumnDef="partnerBandName">
            <th mat-header-cell *matHeaderCellDef>البند</th>
            <td mat-cell *matCellDef="let sale">
              {{ sale.partnerBandName || '-' }}
            </td>
          </ng-container>

          <!-- Representative Column -->
          <ng-container matColumnDef="partnerName">
            <th mat-header-cell *matHeaderCellDef>الشريك</th>
            <td mat-cell *matCellDef="let sale">
                 <span class="partner-chip">
              {{ sale.partnerName || '-' }}</span>
            </td>
          </ng-container>

          <!-- Total Amount Column -->
          <ng-container matColumnDef="amount">
            <th mat-header-cell *matHeaderCellDef>المبلغ</th>
            <td mat-cell *matCellDef="let sale">
              <div class="amount-cell">
                {{ sale.amount ? (sale.amount | currency:'EGP ':'symbol':'1.2-2') : '-' }}
              </div>
            </td>
          </ng-container>

          <!-- Description Column -->
          <ng-container matColumnDef="description">
            <th mat-header-cell *matHeaderCellDef>البيان</th>
            <td mat-cell *matCellDef="let sale">
              {{ sale.description || '-' }}
            </td>
          </ng-container>

          <!-- Notes Column -->
          <ng-container matColumnDef="notes">
            <th mat-header-cell *matHeaderCellDef>الملاحظات</th>
            <td mat-cell *matCellDef="let sale">
              {{ sale.notes || '-' }}
            </td>
          </ng-container>
          <ng-container matColumnDef="imagePath"> 
            <th mat-header-cell *matHeaderCellDef>الصورة</th>
            <td mat-cell *matCellDef="let partner">
               <div class="image-container">
      <img
        [src]="partner.imagePath"
        alt="صورة الحركة"
        class="image-partner"
        (click)="openImagePreview(partner.imagePath)"
        *ngIf="partner.imagePath"
        style="cursor: pointer;"
      />
      <p *ngIf="!partner.imagePath">لا توجد صورة</p>
    </div>
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>الإجراءات</th>
            <td mat-cell *matCellDef="let sale">
              <div class="actions-cell">
                <div *appHasPermission="['Permissions.Partner.Edit']">
                <button mat-icon-button
                        color="primary"
                        matTooltip="تعديل"
                       (click)="editPartnerTransaction(sale)">
                  <mat-icon>edit</mat-icon>
                </button>
                </div>
                <div *appHasPermission="['Permissions.Partner.Delete']">
                <button mat-icon-button
                        color="warn"
                        matTooltip="حذف"
                       (click)="deletePartnerTranseaction(sale)" >
                  <mat-icon>delete</mat-icon>
                </button>
                </div>
              </div>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>

        <!-- No Data Message -->
        <div *ngIf="partnerTransactions.length === 0" class="no-data">
          <mat-icon>receipt_long</mat-icon>
          <p>لا توجد حركات للشركاء</p>
        </div>
      </div>

      <!-- Pagination -->
      <mat-paginator
        *ngIf="totalCount > 0"
        [length]="totalCount"
        [pageSize]="pageSize"
        [pageSizeOptions]="[5, 10, 25, 50]"
        [pageIndex]="pageNumber - 1"
        (page)="onPageChange($event)"
        showFirstLastButtons>
      </mat-paginator>
    </mat-card-content>
  </mat-card>
</div>
