import { Component, Inject, OnInit, ViewChild } from '@angular/core';
import {  FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Category, CreateProductDto, ItemType, Product, UnitOfMeasure, UpdateProductDto  } from '../../../../models/product.model';
import { DialogFormData } from '../../../../models/api-response.model';
import { ProductService } from '../../../../services/product.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatMenuTrigger } from '@angular/material/menu';

@Component({
  selector: 'app-product-list-dialog',
  standalone: false,
  templateUrl: './product-list-dialog.component.html',
  styleUrl: './product-list-dialog.component.css'
})
export class ProductListDialogComponent implements OnInit {

  form!: FormGroup;
  isEditMode = false;
  productId?: number;
  loading = false;
  saving = false;

   itemType = [] as ItemType[];
  

  /// عرض الصورة الاساسية
  imagePathUrl: string | null = null;
  selectedFile: File | null = null;  //اختيار صورة جديدة
  imagePreview: string | null = null; //عرض الصورة اللي مختارة


  categories: Category[] = [];
  unitOfMeasures: UnitOfMeasure[] = [];

selectedCategoryId: number | null = null;

@ViewChild('categoryMenuTrigger') categoryMenuTrigger!: MatMenuTrigger;
onCategorySelected(categoryId: number): void {
  this.selectedCategoryId = categoryId;
  this.form.get('categoryId')?.setValue(categoryId);
  
  // this.categoryMenuTrigger?.closeMenu(); // إغلاق القائمة بعد الاختيار
}

getCategoryName(categoryId: number | null): string {
  if (!categoryId) return '';

  const findName = (categories: Category[]): string | null => {
    for (const cat of categories) {
      if (cat.id === categoryId) return cat.name;
      if (cat.children) {
        const found = findName(cat.children);
        if (found) return found;
      }
    }
    return null;
  };

  return findName(this.categories) || '';
}


  constructor( private fb: FormBuilder,
    private productService: ProductService,
    private snackBar: MatSnackBar,
    private dialogRef: MatDialogRef<DialogFormData<Product>>,
    @Inject(MAT_DIALOG_DATA) public data: DialogFormData<Product>) {
     this.isEditMode = data.mode === 'edit';
    this.form = this.createForm();
    this.loadData();
  }

 ngOnInit(): void {
     if (this.isEditMode && this.data.data) {
      this.populateForm(this.data.data);
      this.imagePathUrl = this.data.data.imagePath;
  }
}

 private createForm(): FormGroup {
    return this.fb.group({
      code: ['', [Validators.required, Validators.maxLength(50)]],
      name: ['', [Validators.required, Validators.maxLength(50)]],
      barcode: ['', [Validators.required, Validators.maxLength(50)]],
      categoryId: ['', Validators.required],
      unitId: ['', Validators.required],
      standardCost: ['', [Validators.required, Validators.min(0)]],
      minimumStock: ['', [Validators.required, Validators.min(0)]],
      maximumStock: ['', [Validators.required,Validators.min(0)]],
      openingBalance: ['', [Validators.required, Validators.min(0)]],
      itemType: ['', Validators.required],
      description: ['', [Validators.maxLength(100)]],
      sortOrder: ['', [Validators.min(0)]],   
      imagePath: [null],
      removeImage: [false]
    },
     {validators: this.minLessThanOrEqualMax,
      updateOn: 'change'
     }   
  );
  }

 private populateForm(product: Product): void {
    this.form.patchValue({
      code: product.code,
      name: product.name,
      barcode: product.barcode,
      categoryId: product.categoryId,
      unitId: product.unitId,
      standardCost: product.standardCost,
      minimumStock: product.minimumStock,
      maximumStock: product.maximumStock,
      openingBalance: product.openingBalance,
      itemType: product.itemType,
      description: product.description,
      sortOrder: product.sortOrder,
      imagePath: product.imagePath     
    });
  }


loadData(): void {
  // Load categories
    this.productService.getCategories().subscribe({
      next: (response) => {
        if (response.succeeded && response.data) {
          this.categories = response.data;

        }
      },
      error: (error) => {
        console.error('Error loading categories:', error);
      }
    });
    
    // Load UnitOfMeasure
    this.productService.getUnitOfMeasure().subscribe({
      next: (response) => {
        if (response.succeeded && response.data) {
          this.unitOfMeasures = response.data;

        }
      },
      error: (error) => {
        console.error('Error loading unitOfMeasure:', error);
      }
    });
    // Load ItemsType
    this.itemType = [
      { id: 1, name: 'أصناف' },
      { id: 2, name: 'خامات' },
      { id: 3, name: 'منتجات' }
    ];
    
   const isGetDeleted = this.isEditMode ? true : false;
   
}

 onSubmit(): void {
    if (this.form.valid) {
      this.loading = true;
      const formValue = this.form.value;
      if (this.selectedFile) {
        formValue.imagePath = this.selectedFile;
      }
      if (this.isEditMode) {
        const updateDto: UpdateProductDto = formValue;
        this.productService.updateProduct(this.data.data!.id, updateDto)
          .subscribe({
            next: (response) => {
              if (response.succeeded && response.data != null) {
                this.dialogRef.close(true);
              }
              this.loading = false;
            },
            error: (error) => {
                this.loading = false;
            }
          });
       } else {
        const createDto: CreateProductDto = formValue;
        this.productService.createProduct(createDto)
          .subscribe({
            next: (response) => {
              if (response.succeeded && response.data != null) {
                this.snackBar.open('تم الاضافة بنجاح', 'إغلاق', { duration: 3000 });
                this.dialogRef.close(true);
              }
              this.loading = false;
            },
            error: (error) => {
             
              this.loading = false;
            }
          });
      }
    }
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }

  getErrorMessage(fieldName: string): string {
    const field = this.form.get(fieldName);
    if (field?.hasError('required')) {
      return 'هذا الحقل مطلوب';
    }

    if (field?.hasError('maxlength')) {
      const maxLength = field.errors?.['maxlength']?.requiredLength;
      return `الحد الأقصى ${maxLength} حرف`;
    }
    if (field?.hasError('min')) {
      return 'يجب أن تكون القيمة أكبر من أو تساوي 0';
    }
    if (field?.hasError('max')) {
      return 'يجب أن تكون القيمة أقل من أو تساوي 100';
    }
    return '';
  }
 

minLessThanOrEqualMax(group: FormGroup): { [key: string]: boolean } | null {
  const min = group.get('minimumStock')?.value;
  const max = group.get('maximumStock')?.value;

  if (min != null && max != null && min > max) {
    return { rangeError: true };
  }
  return null;
}


 onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Validate file type ".jpg", ".jpeg", ".png", ".gif", ".bmp"
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp'];
      if (!allowedTypes.includes(file.type)) {
        this.snackBar.open('نوع الملف غير مدعوم. يرجى اختيار صورة (JPG, PNG, GIF)', 'إغلاق', { duration: 4000 });
        return;
      }

      // Validate file size (5MB max)
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        this.snackBar.open('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت', 'إغلاق', { duration: 4000 });
        return;
      }

      this.selectedFile = file;

      // Create image preview
      const reader = new FileReader();
      reader.onload = (e: any) => {
        this.imagePreview = e.target.result;
      };
      reader.readAsDataURL(file);
    }
}

 removeImage(): void {
    this.selectedFile = null;
    this.imagePreview = null;


}

removeCurrentImage(): void {
  this.imagePathUrl = null;
  this.form.get('removeImage')?.setValue(true);
}


}
