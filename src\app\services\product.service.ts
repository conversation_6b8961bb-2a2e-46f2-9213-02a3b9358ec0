import { Injectable } from '@angular/core';
import { ApiService } from './api.service';
import { Observable } from 'rxjs';
import { ApiResponse, PagedResponse } from '../models/api-response.model';
import { Category, CreateProductDto, Product, UnitOfMeasure, UpdateProductDto } from '../models/product.model';
import { HttpParams } from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class ProductService {

  constructor(private apiService: ApiService) {}

getCategories(): Observable<ApiResponse<Category[]>> {
  return this.apiService.get<Category[]>('Category');
}

 getUnitOfMeasure(): Observable<ApiResponse<UnitOfMeasure[]>> {
  return this.apiService.get<UnitOfMeasure[]>('UnitOfMeasure');
}

getProducts(paged: any): Observable<ApiResponse<PagedResponse<Product>>> {
  let param = new HttpParams();
   param = param.set('pageNumber', paged.pageNumber ?? 1)
   param = param.set('pageSize', paged.pageSize ?? 10)

   if(paged.searchTerm)
   param = param.set('searchTerm', paged.searchTerm || '') 

   if(paged.isActive != null)
    param = param.set('isActive', paged.isActive)

   if(paged.categoryId)
   param = param.set('categoryId', paged.categoryId)

   if(paged.itemType)
   param = param.set('itemType', paged.itemType)

   if(paged.stockStatus)
   param = param.set('stockStatus', paged.stockStatus)

        return this.apiService.get<PagedResponse<Product>>('Product',   {params:param} );

  }

createProduct(product: CreateProductDto) : Observable<ApiResponse<Product>> {
  const formData = new FormData();
  formData.append('Code', product.code);
  formData.append('Barcode', product.barcode); 
  formData.append('Name', product.name); 
  formData.append('Description', product.description); 
  formData.append('CategoryId', product.categoryId.toString()); 
  formData.append('UnitId', product.unitId.toString()); 
  formData.append('StandardCost', product.standardCost.toString()); 
  formData.append('MinimumStock', product.minimumStock?.toString() || ''); 
  formData.append('MaximumStock', product.maximumStock?.toString() || ''); 
  formData.append('OpeningBalance', product.openingBalance.toString()); 
  formData.append('SortOrder', product.sortOrder?.toString() || ''); 
  formData.append('ItemType', product.itemType?.toString() || ''); 
  if (product.imagePath) {
    formData.append('ImagePath', product.imagePath);
  }
    return this.apiService.post<Product>('Product', formData);
  }

updateProduct(Id : number,product: UpdateProductDto) : Observable<ApiResponse<Product>> {
  
  product.id = Id;
  const formData = new FormData();
    formData.append('Id', product.id.toString());
    formData.append('Code', product.code);
    formData.append('Barcode', product.barcode); 
    formData.append('Name', product.name); 
    formData.append('Description', product.description); 
    formData.append('CategoryId', product.categoryId.toString()); 
    formData.append('UnitId', product.unitId.toString()); 
    formData.append('StandardCost', product.standardCost.toString()); 
    formData.append('MinimumStock', product.minimumStock?.toString() || '');  
    formData.append('MaximumStock', product.maximumStock?.toString() || ''); 
    formData.append('OpeningBalance', product.openingBalance.toString()); 
    formData.append('SortOrder', product.sortOrder?.toString() || ''); 
    formData.append('ItemType', product.itemType?.toString() || ''); 
    if (product.imagePath) {
      formData.append('ImagePath', product.imagePath);
    }
    formData.append('RemoveImage', product.removeImage.toString());
    
      return this.apiService.put<Product>('Product', formData);
  }

deleteProduct(Id : number) : Observable<ApiResponse<Product>> {
    return this.apiService.delete<Product>(`Product/${Id}`);
 }


   
}
